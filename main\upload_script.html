<!DOCTYPE html>
<html>
<head>
    <title>TPM Jar Management</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        input[type="text"], input[type="file"] { width: 100%; padding: 5px; box-sizing: border-box; }
        button { padding: 8px 16px; margin: 2px; cursor: pointer; }
        .section { margin-bottom: 30px; border: 1px solid #ccc; padding: 15px; }
        .section h3 { margin-top: 0; }
        .warning { color: red; font-weight: bold; }
        .info { background-color: #e7f3ff; padding: 10px; border-left: 4px solid #2196F3; }
    </style>
</head>
<body>
    <h1>TPM Jar Management</h1>

    <!-- Device ID Management Section -->
    <div class="section">
        <h3>Device ID Management</h3>
        <table>
            <tr>
                <th width="200px">Current Device ID</th>
                <td id="currentDeviceId">Loading...</td>
                <td width="100px"><button onclick="getCurrentId()">Refresh</button></td>
            </tr>
            <tr>
                <th>Set New Device ID</th>
                <td><input type="text" id="newDeviceId" placeholder="Enter ID (1-255)" maxlength="3"></td>
                <td><button onclick="setDeviceId()">Set ID</button></td>
            </tr>
        </table>
    </div>

    <!-- Firmware Update Section -->
    <div class="section">
        <h3>Firmware Update (OTA)</h3>
        <div class="warning">Warning: Only upload valid ESP32 firmware (.bin) files!</div>
        <table>
            <tr>
                <th width="200px">Select Firmware</th>
                <td><input type="file" id="firmwarefile" accept=".bin"></td>
            </tr>
            <tr>
                <th>Action</th>
                <td colspan="2">
                    <button id="otaupload" onclick="uploadFirmware()" style="background-color: #ff6b6b; color: white; font-weight: bold;">
                        Update Firmware
                    </button>
                </td>
            </tr>
        </table>
        <div class="info">Max firmware size: 2MB. Device will restart after update.</div>
    </div>

    <!-- File Upload Section -->
    <div class="section">
        <h3>File Upload</h3>
        <table>
            <tr>
                <th width="200px">Select File</th>
                <td><input type="file" id="newfile" onchange="setpath()"></td>
            </tr>
            <tr>
                <th>Server Path</th>
                <td><input type="text" id="filepath" placeholder="File path on server"></td>
                <td width="100px"><button id="upload" onclick="upload()">Upload</button></td>
            </tr>
        </table>
        <div class="info">Max file size: 512KB</div>
    </div>
    <script>
        // Load device ID when page loads
        window.onload = function() {
            getCurrentId();
        };

        // Device ID Management Functions
        function getCurrentId() {
            var xhttp = new XMLHttpRequest();
            xhttp.onreadystatechange = function() {
                if (xhttp.readyState == 4) {
                    if (xhttp.status == 200) {
                        try {
                            var response = JSON.parse(xhttp.responseText);
                            document.getElementById("currentDeviceId").innerHTML = response.device_id;
                        } catch (e) {
                            document.getElementById("currentDeviceId").innerHTML = xhttp.responseText;
                        }
                    } else {
                        document.getElementById("currentDeviceId").innerHTML = "Error loading ID";
                    }
                }
            };
            xhttp.open("GET", "/api/device_id", true);
            xhttp.send();
        }

        function setDeviceId() {
            var deviceId = document.getElementById("newDeviceId").value.trim();

            if (deviceId === "") {
                alert("Please enter a device ID!");
                return;
            }

            var id = parseInt(deviceId);
            if (isNaN(id) || id < 1 || id > 255) {
                alert("Device ID must be a number between 1 and 255!");
                return;
            }

            if (!confirm("Are you sure you want to change the device ID to " + id + "?")) {
                return;
            }

            var xhttp = new XMLHttpRequest();
            xhttp.onreadystatechange = function() {
                if (xhttp.readyState == 4) {
                    if (xhttp.status == 200) {
                        alert("Device ID updated successfully!");
                        document.getElementById("newDeviceId").value = "";
                        getCurrentId();
                    } else {
                        alert("Failed to update device ID: " + xhttp.responseText);
                    }
                }
            };
            xhttp.open("POST", "/api/device_id", true);
            xhttp.setRequestHeader("Content-Type", "application/json");
            xhttp.send(JSON.stringify({device_id: id}));
        }

        // File Upload Functions
        function setpath() {
            var default_path = document.getElementById("newfile").files[0].name;
            document.getElementById("filepath").value = default_path;
        }

        function upload() {
            var filePath = document.getElementById("filepath").value;
            var upload_path = "/upload/" + filePath;
            var fileInput = document.getElementById("newfile").files;

            var MAX_FILE_SIZE = 512*1024;
            var MAX_FILE_SIZE_STR = "512KB";

            if (fileInput.length == 0) {
                alert("No file selected!");
            } else if (filePath.length == 0) {
                alert("File path on server is not set!");
            } else if (filePath.indexOf(' ') >= 0) {
                alert("File path on server cannot have spaces!");
            } else if (filePath[filePath.length-1] == '/') {
                alert("File name not specified after path!");
            } else if (fileInput[0].size > MAX_FILE_SIZE) {
                alert("File size must be less than " + MAX_FILE_SIZE_STR + "!");
            } else {
                document.getElementById("newfile").disabled = true;
                document.getElementById("filepath").disabled = true;
                document.getElementById("upload").disabled = true;
                document.getElementById("upload").innerHTML = "Uploading...";

                var file = fileInput[0];
                var xhttp = new XMLHttpRequest();
                xhttp.onreadystatechange = function() {
                    if (xhttp.readyState == 4) {
                        if (xhttp.status == 200) {
                            alert("File uploaded successfully!");
                            document.getElementById("newfile").disabled = false;
                            document.getElementById("filepath").disabled = false;
                            document.getElementById("upload").disabled = false;
                            document.getElementById("upload").innerHTML = "Upload";
                            document.getElementById("newfile").value = "";
                            document.getElementById("filepath").value = "";
                        } else if (xhttp.status == 0) {
                            alert("Server closed the connection abruptly!");
                            location.reload()
                        } else {
                            alert(xhttp.status + " Error!\n" + xhttp.responseText);
                            document.getElementById("newfile").disabled = false;
                            document.getElementById("filepath").disabled = false;
                            document.getElementById("upload").disabled = false;
                            document.getElementById("upload").innerHTML = "Upload";
                        }
                    }
                };
                xhttp.open("POST", upload_path, true);
                xhttp.send(file);
            }
        }

        // Firmware Update Functions
        function uploadFirmware() {
            var fileInput = document.getElementById("firmwarefile").files;

            if (fileInput.length == 0) {
                alert("No firmware file selected!");
                return;
            }

            var file = fileInput[0];

            // Check if file has .bin extension
            if (!file.name.toLowerCase().endsWith('.bin')) {
                alert("Please select a valid firmware file with .bin extension!");
                return;
            }

            // Check firmware file size (2MB limit)
            if (file.size > 2*1024*1024) {
                alert("Firmware file size must be less than 2MB!");
                return;
            }

            // Confirm firmware update
            if (!confirm("Are you sure you want to update the firmware?\nThis will restart the device!")) {
                return;
            }

            document.getElementById("firmwarefile").disabled = true;
            document.getElementById("otaupload").disabled = true;
            document.getElementById("otaupload").innerHTML = "Updating...";

            var xhttp = new XMLHttpRequest();
            xhttp.onreadystatechange = function() {
                if (xhttp.readyState == 4) {
                    if (xhttp.status == 200) {
                        alert("Firmware update successful! Device will restart.");
                        setTimeout(function() {
                            location.reload();
                        }, 5000);
                    } else if (xhttp.status == 0) {
                        alert("Device is restarting after firmware update...");
                        setTimeout(function() {
                            location.reload();
                        }, 10000);
                    } else {
                        alert("Firmware update failed: " + xhttp.status + " - " + xhttp.responseText);
                        document.getElementById("firmwarefile").disabled = false;
                        document.getElementById("otaupload").disabled = false;
                        document.getElementById("otaupload").innerHTML = "Update Firmware";
                    }
                }
            };

            xhttp.open("POST", "/ota", true);
            xhttp.send(file);
        }
    </script>
</body>
</html>
