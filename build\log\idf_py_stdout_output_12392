[0/1] C:\WINDOWS\system32\cmd.exe /C "cd /D E:\esp32_space\ble_mill_monitor_slave\build && E:\esp_tool\.espressif\python_env\idf5.3_py3.11_env\Scripts\python.exe E:/esp/v5.3.3/esp-idf/tools/kconfig_new/prepare_kconfig_files.py --list-separator=semicolon --env-file E:/esp32_space/ble_mill_monitor_slave/build/config.env && E:\esp_tool\.espressif\python_env\idf5.3_py3.11_env\Scripts\python.exe -m kconfserver --env-file E:/esp32_space/ble_mill_monitor_slave/build/config.env --kconfig E:/esp/v5.3.3/esp-idf/Kconfig --sdkconfig-rename E:/esp/v5.3.3/esp-idf/sdkconfig.rename --config E:/esp32_space/ble_mill_monitor_slave/sdkconfig"
{"version": 2, "values": {"SOC_ADC_SUPPORTED": true, "SOC_DEDICATED_GPIO_SUPPORTED": true, "SOC_UART_SUPPORTED": true, "SOC_GDMA_SUPPORTED": true, "SOC_AHB_GDMA_SUPPORTED": true, "SOC_GPTIMER_SUPPORTED": true, "SOC_TWAI_SUPPORTED": true, "SOC_BT_SUPPORTED": true, "SOC_ASYNC_MEMCPY_SUPPORTED": true, "SOC_USB_SERIAL_JTAG_SUPPORTED": true, "SOC_TEMP_SENSOR_SUPPORTED": true, "SOC_XT_WDT_SUPPORTED": true, "SOC_PHY_SUPPORTED": true, "SOC_WIFI_SUPPORTED": true, "SOC_SUPPORTS_SECURE_DL_MODE": true, "SOC_EFUSE_KEY_PURPOSE_FIELD": true, "SOC_EFUSE_HAS_EFUSE_RST_BUG": true, "SOC_EFUSE_SUPPORTED": true, "SOC_RTC_FAST_MEM_SUPPORTED": true, "SOC_RTC_MEM_SUPPORTED": true, "SOC_I2S_SUPPORTED": true, "SOC_RMT_SUPPORTED": true, "SOC_SDM_SUPPORTED": true, "SOC_GPSPI_SUPPORTED": true, "SOC_LEDC_SUPPORTED": true, "SOC_I2C_SUPPORTED": true, "SOC_SYSTIMER_SUPPORTED": true, "SOC_SUPPORT_COEXISTENCE": true, "SOC_AES_SUPPORTED": true, "SOC_MPI_SUPPORTED": true, "SOC_SHA_SUPPORTED": true, "SOC_HMAC_SUPPORTED": true, "SOC_DIG_SIGN_SUPPORTED": true, "SOC_FLASH_ENC_SUPPORTED": true, "SOC_SECURE_BOOT_SUPPORTED": true, "SOC_MEMPROT_SUPPORTED": true, "SOC_BOD_SUPPORTED": true, "SOC_CLK_TREE_SUPPORTED": true, "SOC_ASSIST_DEBUG_SUPPORTED": true, "SOC_WDT_SUPPORTED": true, "SOC_SPI_FLASH_SUPPORTED": true, "SOC_RNG_SUPPORTED": true, "SOC_LIGHT_SLEEP_SUPPORTED": true, "SOC_DEEP_SLEEP_SUPPORTED": true, "SOC_LP_PERIPH_SHARE_INTERRUPT": true, "SOC_PM_SUPPORTED": true, "SOC_XTAL_SUPPORT_40M": true, "SOC_AES_SUPPORT_DMA": true, "SOC_AES_GDMA": true, "SOC_AES_SUPPORT_AES_128": true, "SOC_AES_SUPPORT_AES_256": true, "SOC_ADC_DIG_CTRL_SUPPORTED": true, "SOC_ADC_ARBITER_SUPPORTED": true, "SOC_ADC_DIG_IIR_FILTER_SUPPORTED": true, "SOC_ADC_MONITOR_SUPPORTED": true, "SOC_ADC_DMA_SUPPORTED": true, "SOC_ADC_PERIPH_NUM": 2, "SOC_ADC_MAX_CHANNEL_NUM": 5, "SOC_ADC_ATTEN_NUM": 4, "SOC_ADC_DIGI_CONTROLLER_NUM": 1, "SOC_ADC_PATT_LEN_MAX": 8, "SOC_ADC_DIGI_MIN_BITWIDTH": 12, "SOC_ADC_DIGI_MAX_BITWIDTH": 12, "SOC_ADC_DIGI_RESULT_BYTES": 4, "SOC_ADC_DIGI_DATA_BYTES_PER_CONV": 4, "SOC_ADC_DIGI_IIR_FILTER_NUM": 2, "SOC_ADC_DIGI_MONITOR_NUM": 2, "SOC_ADC_SAMPLE_FREQ_THRES_HIGH": 83333, "SOC_ADC_SAMPLE_FREQ_THRES_LOW": 611, "SOC_ADC_RTC_MIN_BITWIDTH": 12, "SOC_ADC_RTC_MAX_BITWIDTH": 12, "SOC_ADC_CALIBRATION_V1_SUPPORTED": true, "SOC_ADC_SELF_HW_CALI_SUPPORTED": true, "SOC_ADC_SHARED_POWER": true, "SOC_APB_BACKUP_DMA": true, "SOC_BROWNOUT_RESET_SUPPORTED": true, "SOC_SHARED_IDCACHE_SUPPORTED": true, "SOC_CACHE_MEMORY_IBANK_SIZE": 16384, "SOC_CPU_CORES_NUM": 1, "SOC_CPU_INTR_NUM": 32, "SOC_CPU_HAS_FLEXIBLE_INTC": true, "SOC_CPU_HAS_CSR_PC": true, "SOC_CPU_BREAKPOINTS_NUM": 8, "SOC_CPU_WATCHPOINTS_NUM": 8, "SOC_CPU_WATCHPOINT_MAX_REGION_SIZE": **********, "SOC_DS_SIGNATURE_MAX_BIT_LEN": 3072, "SOC_DS_KEY_PARAM_MD_IV_LENGTH": 16, "SOC_DS_KEY_CHECK_MAX_WAIT_US": 1100, "SOC_AHB_GDMA_VERSION": 1, "SOC_GDMA_NUM_GROUPS_MAX": 1, "SOC_GDMA_PAIRS_PER_GROUP_MAX": 3, "SOC_GPIO_PORT": 1, "SOC_GPIO_PIN_COUNT": 22, "SOC_GPIO_SUPPORT_PIN_GLITCH_FILTER": true, "SOC_GPIO_FILTER_CLK_SUPPORT_APB": true, "SOC_GPIO_SUPPORT_FORCE_HOLD": true, "SOC_GPIO_SUPPORT_DEEPSLEEP_WAKEUP": true, "SOC_GPIO_IN_RANGE_MAX": 21, "SOC_GPIO_OUT_RANGE_MAX": 21, "SOC_GPIO_DEEP_SLEEP_WAKE_VALID_GPIO_MASK": 0, "SOC_GPIO_DEEP_SLEEP_WAKE_SUPPORTED_PIN_CNT": 6, "SOC_GPIO_VALID_DIGITAL_IO_PAD_MASK": 4194240, "SOC_GPIO_CLOCKOUT_BY_GPIO_MATRIX": true, "SOC_GPIO_CLOCKOUT_CHANNEL_NUM": 3, "SOC_GPIO_SUPPORT_HOLD_IO_IN_DSLP": true, "SOC_DEDIC_GPIO_OUT_CHANNELS_NUM": 8, "SOC_DEDIC_GPIO_IN_CHANNELS_NUM": 8, "SOC_DEDIC_PERIPH_ALWAYS_ENABLE": true, "SOC_I2C_NUM": 1, "SOC_HP_I2C_NUM": 1, "SOC_I2C_FIFO_LEN": 32, "SOC_I2C_CMD_REG_NUM": 8, "SOC_I2C_SUPPORT_SLAVE": true, "SOC_I2C_SUPPORT_HW_CLR_BUS": true, "SOC_I2C_SUPPORT_XTAL": true, "SOC_I2C_SUPPORT_RTC": true, "SOC_I2C_SUPPORT_10BIT_ADDR": true, "SOC_I2C_SLAVE_SUPPORT_BROADCAST": true, "SOC_I2C_SLAVE_CAN_GET_STRETCH_CAUSE": true, "SOC_I2C_SLAVE_SUPPORT_I2CRAM_ACCESS": true, "SOC_I2S_NUM": 1, "SOC_I2S_HW_VERSION_2": true, "SOC_I2S_SUPPORTS_XTAL": true, "SOC_I2S_SUPPORTS_PLL_F160M": true, "SOC_I2S_SUPPORTS_PCM": true, "SOC_I2S_SUPPORTS_PDM": true, "SOC_I2S_SUPPORTS_PDM_TX": true, "SOC_I2S_PDM_MAX_TX_LINES": 2, "SOC_I2S_SUPPORTS_TDM": true, "SOC_LEDC_SUPPORT_APB_CLOCK": true, "SOC_LEDC_SUPPORT_XTAL_CLOCK": true, "SOC_LEDC_CHANNEL_NUM": 6, "SOC_LEDC_TIMER_BIT_WIDTH": 14, "SOC_LEDC_SUPPORT_FADE_STOP": true, "SOC_MMU_LINEAR_ADDRESS_REGION_NUM": 1, "SOC_MMU_PERIPH_NUM": 1, "SOC_MPU_MIN_REGION_SIZE": 536870912, "SOC_MPU_REGIONS_MAX_NUM": 8, "SOC_RMT_GROUPS": 1, "SOC_RMT_TX_CANDIDATES_PER_GROUP": 2, "SOC_RMT_RX_CANDIDATES_PER_GROUP": 2, "SOC_RMT_CHANNELS_PER_GROUP": 4, "SOC_RMT_MEM_WORDS_PER_CHANNEL": 48, "SOC_RMT_SUPPORT_RX_PINGPONG": true, "SOC_RMT_SUPPORT_RX_DEMODULATION": true, "SOC_RMT_SUPPORT_TX_ASYNC_STOP": true, "SOC_RMT_SUPPORT_TX_LOOP_COUNT": true, "SOC_RMT_SUPPORT_TX_SYNCHRO": true, "SOC_RMT_SUPPORT_TX_CARRIER_DATA_ONLY": true, "SOC_RMT_SUPPORT_XTAL": true, "SOC_RMT_SUPPORT_APB": true, "SOC_RMT_SUPPORT_RC_FAST": true, "SOC_RTC_CNTL_CPU_PD_DMA_BUS_WIDTH": 128, "SOC_RTC_CNTL_CPU_PD_REG_FILE_NUM": 108, "SOC_SLEEP_SYSTIMER_STALL_WORKAROUND": true, "SOC_SLEEP_TGWDT_STOP_WORKAROUND": true, "SOC_RTCIO_PIN_COUNT": 0, "SOC_MPI_MEM_BLOCKS_NUM": 4, "SOC_MPI_OPERATIONS_NUM": 3, "SOC_RSA_MAX_BIT_LEN": 3072, "SOC_SHA_DMA_MAX_BUFFER_SIZE": 3968, "SOC_SHA_SUPPORT_DMA": true, "SOC_SHA_SUPPORT_RESUME": true, "SOC_SHA_GDMA": true, "SOC_SHA_SUPPORT_SHA1": true, "SOC_SHA_SUPPORT_SHA224": true, "SOC_SHA_SUPPORT_SHA256": true, "SOC_SDM_GROUPS": 1, "SOC_SDM_CHANNELS_PER_GROUP": 4, "SOC_SDM_CLK_SUPPORT_APB": true, "SOC_SPI_PERIPH_NUM": 2, "SOC_SPI_MAX_CS_NUM": 6, "SOC_SPI_MAXIMUM_BUFFER_SIZE": 64, "SOC_SPI_SUPPORT_DDRCLK": true, "SOC_SPI_SLAVE_SUPPORT_SEG_TRANS": true, "SOC_SPI_SUPPORT_CD_SIG": true, "SOC_SPI_SUPPORT_CONTINUOUS_TRANS": true, "SOC_SPI_SUPPORT_SLAVE_HD_VER2": true, "SOC_SPI_SUPPORT_CLK_APB": true, "SOC_SPI_SUPPORT_CLK_XTAL": true, "SOC_SPI_PERIPH_SUPPORT_CONTROL_DUMMY_OUT": true, "SOC_SPI_SCT_SUPPORTED": true, "SOC_SPI_SCT_REG_NUM": 14, "SOC_SPI_SCT_BUFFER_NUM_MAX": true, "SOC_SPI_SCT_CONF_BITLEN_MAX": 262138, "SOC_MEMSPI_IS_INDEPENDENT": true, "SOC_SPI_MAX_PRE_DIVIDER": 16, "SOC_SPI_MEM_SUPPORT_AUTO_WAIT_IDLE": true, "SOC_SPI_MEM_SUPPORT_AUTO_SUSPEND": true, "SOC_SPI_MEM_SUPPORT_AUTO_RESUME": true, "SOC_SPI_MEM_SUPPORT_IDLE_INTR": true, "SOC_SPI_MEM_SUPPORT_SW_SUSPEND": true, "SOC_SPI_MEM_SUPPORT_CHECK_SUS": true, "SOC_SPI_MEM_SUPPORT_CONFIG_GPIO_BY_EFUSE": true, "SOC_SPI_MEM_SUPPORT_WRAP": true, "SOC_MEMSPI_SRC_FREQ_80M_SUPPORTED": true, "SOC_MEMSPI_SRC_FREQ_40M_SUPPORTED": true, "SOC_MEMSPI_SRC_FREQ_26M_SUPPORTED": true, "SOC_MEMSPI_SRC_FREQ_20M_SUPPORTED": true, "SOC_SYSTIMER_COUNTER_NUM": 2, "SOC_SYSTIMER_ALARM_NUM": 3, "SOC_SYSTIMER_BIT_WIDTH_LO": 32, "SOC_SYSTIMER_BIT_WIDTH_HI": 20, "SOC_SYSTIMER_FIXED_DIVIDER": true, "SOC_SYSTIMER_INT_LEVEL": true, "SOC_SYSTIMER_ALARM_MISS_COMPENSATE": true, "SOC_TIMER_GROUPS": 2, "SOC_TIMER_GROUP_TIMERS_PER_GROUP": 1, "SOC_TIMER_GROUP_COUNTER_BIT_WIDTH": 54, "SOC_TIMER_GROUP_SUPPORT_XTAL": true, "SOC_TIMER_GROUP_SUPPORT_APB": true, "SOC_TIMER_GROUP_TOTAL_TIMERS": 2, "SOC_LP_TIMER_BIT_WIDTH_LO": 32, "SOC_LP_TIMER_BIT_WIDTH_HI": 16, "SOC_MWDT_SUPPORT_XTAL": true, "SOC_TWAI_CONTROLLER_NUM": 1, "SOC_TWAI_CLK_SUPPORT_APB": true, "SOC_TWAI_BRP_MIN": 2, "SOC_TWAI_BRP_MAX": 16384, "SOC_TWAI_SUPPORTS_RX_STATUS": true, "SOC_EFUSE_DIS_DOWNLOAD_ICACHE": true, "SOC_EFUSE_DIS_PAD_JTAG": true, "SOC_EFUSE_DIS_USB_JTAG": true, "SOC_EFUSE_DIS_DIRECT_BOOT": true, "SOC_EFUSE_SOFT_DIS_JTAG": true, "SOC_EFUSE_DIS_ICACHE": true, "SOC_EFUSE_BLOCK9_KEY_PURPOSE_QUIRK": true, "SOC_SECURE_BOOT_V2_RSA": true, "SOC_EFUSE_SECURE_BOOT_KEY_DIGESTS": 3, "SOC_EFUSE_REVOKE_BOOT_KEY_DIGESTS": true, "SOC_SUPPORT_SECURE_BOOT_REVOKE_KEY": true, "SOC_FLASH_ENCRYPTED_XTS_AES_BLOCK_MAX": 32, "SOC_FLASH_ENCRYPTION_XTS_AES": true, "SOC_FLASH_ENCRYPTION_XTS_AES_128": true, "SOC_MEMPROT_CPU_PREFETCH_PAD_SIZE": 16, "SOC_MEMPROT_MEM_ALIGN_SIZE": 512, "SOC_UART_NUM": 2, "SOC_UART_HP_NUM": 2, "SOC_UART_FIFO_LEN": 128, "SOC_UART_BITRATE_MAX": 5000000, "SOC_UART_SUPPORT_APB_CLK": true, "SOC_UART_SUPPORT_RTC_CLK": true, "SOC_UART_SUPPORT_XTAL_CLK": true, "SOC_UART_SUPPORT_WAKEUP_INT": true, "SOC_UART_SUPPORT_FSM_TX_WAIT_SEND": true, "SOC_COEX_HW_PTI": true, "SOC_PHY_DIG_REGS_MEM_SIZE": 21, "SOC_MAC_BB_PD_MEM_SIZE": 192, "SOC_WIFI_LIGHT_SLEEP_CLK_WIDTH": 12, "SOC_PM_SUPPORT_WIFI_WAKEUP": true, "SOC_PM_SUPPORT_BT_WAKEUP": true, "SOC_PM_SUPPORT_CPU_PD": true, "SOC_PM_SUPPORT_WIFI_PD": true, "SOC_PM_SUPPORT_BT_PD": true, "SOC_PM_SUPPORT_RC_FAST_PD": true, "SOC_PM_SUPPORT_VDDSDIO_PD": true, "SOC_PM_SUPPORT_MAC_BB_PD": true, "SOC_PM_CPU_RETENTION_BY_RTCCNTL": true, "SOC_PM_MODEM_RETENTION_BY_BACKUPDMA": true, "SOC_CLK_RC_FAST_D256_SUPPORTED": true, "SOC_RTC_SLOW_CLK_SUPPORT_RC_FAST_D256": true, "SOC_CLK_RC_FAST_SUPPORT_CALIBRATION": true, "SOC_CLK_XTAL32K_SUPPORTED": true, "SOC_TEMPERATURE_SENSOR_SUPPORT_FAST_RC": true, "SOC_TEMPERATURE_SENSOR_SUPPORT_XTAL": true, "SOC_WIFI_HW_TSF": true, "SOC_WIFI_FTM_SUPPORT": true, "SOC_WIFI_GCMP_SUPPORT": true, "SOC_WIFI_WAPI_SUPPORT": true, "SOC_WIFI_CSI_SUPPORT": true, "SOC_WIFI_MESH_SUPPORT": true, "SOC_WIFI_SUPPORT_VARIABLE_BEACON_WINDOW": true, "SOC_WIFI_PHY_NEEDS_USB_WORKAROUND": true, "SOC_BLE_SUPPORTED": true, "SOC_BLE_MESH_SUPPORTED": true, "SOC_BLE_50_SUPPORTED": true, "SOC_BLE_DEVICE_PRIVACY_SUPPORTED": true, "SOC_BLUFI_SUPPORTED": true, "SOC_PHY_COMBO_MODULE": true, "IDF_CMAKE": true, "IDF_TOOLCHAIN": "gcc", "IDF_TARGET_ARCH_RISCV": true, "IDF_TARGET_ARCH": "riscv", "IDF_TARGET": "esp32c3", "IDF_INIT_VERSION": "$IDF_INIT_VERSION", "IDF_TARGET_ESP32C3": true, "IDF_FIRMWARE_CHIP_ID": 5, "APP_BUILD_TYPE_APP_2NDBOOT": true, "APP_BUILD_TYPE_RAM": false, "APP_BUILD_GENERATE_BINARIES": true, "APP_BUILD_BOOTLOADER": true, "APP_BUILD_USE_FLASH_SECTIONS": true, "APP_REPRODUCIBLE_BUILD": false, "APP_NO_BLOBS": false, "BOOTLOADER_COMPILE_TIME_DATE": true, "BOOTLOADER_PROJECT_VER": 1, "BOOTLOADER_OFFSET_IN_FLASH": 0, "BOOTLOADER_COMPILER_OPTIMIZATION_SIZE": true, "BOOTLOADER_COMPILER_OPTIMIZATION_DEBUG": false, "BOOTLOADER_COMPILER_OPTIMIZATION_PERF": false, "BOOTLOADER_COMPILER_OPTIMIZATION_NONE": false, "BOOTLOADER_LOG_LEVEL_NONE": false, "BOOTLOADER_LOG_LEVEL_ERROR": false, "BOOTLOADER_LOG_LEVEL_WARN": false, "BOOTLOADER_LOG_LEVEL_INFO": true, "BOOTLOADER_LOG_LEVEL_DEBUG": false, "BOOTLOADER_LOG_LEVEL_VERBOSE": false, "BOOTLOADER_LOG_LEVEL": 3, "BOOTLOADER_FLASH_DC_AWARE": false, "BOOTLOADER_FLASH_XMC_SUPPORT": true, "BOOTLOADER_FACTORY_RESET": false, "BOOTLOADER_APP_TEST": false, "BOOTLOADER_REGION_PROTECTION_ENABLE": true, "BOOTLOADER_WDT_ENABLE": true, "BOOTLOADER_WDT_DISABLE_IN_USER_CODE": false, "BOOTLOADER_WDT_TIME_MS": 9000, "BOOTLOADER_APP_ROLLBACK_ENABLE": false, "BOOTLOADER_SKIP_VALIDATE_IN_DEEP_SLEEP": false, "BOOTLOADER_SKIP_VALIDATE_ON_POWER_ON": false, "BOOTLOADER_SKIP_VALIDATE_ALWAYS": false, "BOOTLOADER_RESERVE_RTC_SIZE": 0, "BOOTLOADER_CUSTOM_RESERVE_RTC": false, "SECURE_BOOT_V2_RSA_SUPPORTED": true, "SECURE_BOOT_V2_PREFERRED": true, "SECURE_SIGNED_APPS_NO_SECURE_BOOT": false, "SECURE_BOOT": false, "SECURE_FLASH_ENC_ENABLED": false, "SECURE_ROM_DL_MODE_ENABLED": true, "APP_COMPILE_TIME_DATE": true, "APP_EXCLUDE_PROJECT_VER_VAR": false, "APP_EXCLUDE_PROJECT_NAME_VAR": false, "APP_PROJECT_VER_FROM_CONFIG": false, "APP_RETRIEVE_LEN_ELF_SHA": 9, "ESP_ROM_HAS_CRC_LE": true, "ESP_ROM_HAS_CRC_BE": true, "ESP_ROM_HAS_MZ_CRC32": true, "ESP_ROM_HAS_JPEG_DECODE": true, "ESP_ROM_UART_CLK_IS_XTAL": true, "ESP_ROM_USB_SERIAL_DEVICE_NUM": 3, "ESP_ROM_HAS_RETARGETABLE_LOCKING": true, "ESP_ROM_HAS_ERASE_0_REGION_BUG": true, "ESP_ROM_HAS_ENCRYPTED_WRITES_USING_LEGACY_DRV": true, "ESP_ROM_GET_CLK_FREQ": true, "ESP_ROM_NEEDS_SWSETUP_WORKAROUND": true, "ESP_ROM_HAS_LAYOUT_TABLE": true, "ESP_ROM_HAS_SPI_FLASH": true, "ESP_ROM_HAS_ETS_PRINTF_BUG": true, "ESP_ROM_HAS_NEWLIB": true, "ESP_ROM_HAS_NEWLIB_NANO_FORMAT": true, "ESP_ROM_HAS_NEWLIB_32BIT_TIME": true, "ESP_ROM_NEEDS_SET_CACHE_MMU_SIZE": true, "ESP_ROM_RAM_APP_NEEDS_MMU_INIT": true, "ESP_ROM_HAS_SW_FLOAT": true, "ESP_ROM_USB_OTG_NUM": -1, "ESP_ROM_HAS_VERSION": true, "ESP_ROM_SUPPORT_DEEP_SLEEP_WAKEUP_STUB": true, "BOOT_ROM_LOG_ALWAYS_ON": true, "BOOT_ROM_LOG_ALWAYS_OFF": false, "BOOT_ROM_LOG_ON_GPIO_HIGH": false, "BOOT_ROM_LOG_ON_GPIO_LOW": false, "ESPTOOLPY_NO_STUB": false, "ESPTOOLPY_FLASHMODE_QIO": false, "ESPTOOLPY_FLASHMODE_QOUT": false, "ESPTOOLPY_FLASHMODE_DIO": true, "ESPTOOLPY_FLASHMODE_DOUT": false, "ESPTOOLPY_FLASH_SAMPLE_MODE_STR": true, "ESPTOOLPY_FLASHMODE": "dio", "ESPTOOLPY_FLASHFREQ_80M": true, "ESPTOOLPY_FLASHFREQ_40M": false, "ESPTOOLPY_FLASHFREQ_26M": false, "ESPTOOLPY_FLASHFREQ_20M": false, "ESPTOOLPY_FLASHFREQ_80M_DEFAULT": true, "ESPTOOLPY_FLASHFREQ": "80m", "ESPTOOLPY_FLASHSIZE_1MB": false, "ESPTOOLPY_FLASHSIZE_2MB": false, "ESPTOOLPY_FLASHSIZE_4MB": true, "ESPTOOLPY_FLASHSIZE_8MB": false, "ESPTOOLPY_FLASHSIZE_16MB": false, "ESPTOOLPY_FLASHSIZE_32MB": false, "ESPTOOLPY_FLASHSIZE_64MB": false, "ESPTOOLPY_FLASHSIZE_128MB": false, "ESPTOOLPY_FLASHSIZE": "4MB", "ESPTOOLPY_HEADER_FLASHSIZE_UPDATE": false, "ESPTOOLPY_BEFORE_RESET": true, "ESPTOOLPY_BEFORE_NORESET": false, "ESPTOOLPY_BEFORE": "default_reset", "ESPTOOLPY_AFTER_RESET": true, "ESPTOOLPY_AFTER_NORESET": false, "ESPTOOLPY_AFTER": "hard_reset", "ESPTOOLPY_MONITOR_BAUD": 115200, "PARTITION_TABLE_SINGLE_APP": false, "PARTITION_TABLE_SINGLE_APP_LARGE": false, "PARTITION_TABLE_TWO_OTA": false, "PARTITION_TABLE_CUSTOM": true, "PARTITION_TABLE_CUSTOM_FILENAME": "partitions.csv", "PARTITION_TABLE_FILENAME": "partitions.csv", "PARTITION_TABLE_OFFSET": 32768, "PARTITION_TABLE_MD5": true, "COMPILER_OPTIMIZATION_DEBUG": true, "COMPILER_OPTIMIZATION_SIZE": false, "COMPILER_OPTIMIZATION_PERF": false, "COMPILER_OPTIMIZATION_NONE": false, "COMPILER_OPTIMIZATION_ASSERTIONS_ENABLE": true, "COMPILER_OPTIMIZATION_ASSERTIONS_SILENT": false, "COMPILER_OPTIMIZATION_ASSERTIONS_DISABLE": false, "COMPILER_FLOAT_LIB_FROM_GCCLIB": true, "COMPILER_OPTIMIZATION_ASSERTION_LEVEL": 2, "COMPILER_OPTIMIZATION_CHECKS_SILENT": false, "COMPILER_HIDE_PATHS_MACROS": true, "COMPILER_CXX_EXCEPTIONS": false, "COMPILER_CXX_RTTI": false, "COMPILER_STACK_CHECK_MODE_NONE": true, "COMPILER_STACK_CHECK_MODE_NORM": false, "COMPILER_STACK_CHECK_MODE_STRONG": false, "COMPILER_STACK_CHECK_MODE_ALL": false, "COMPILER_WARN_WRITE_STRINGS": false, "COMPILER_SAVE_RESTORE_LIBCALLS": false, "COMPILER_DISABLE_GCC12_WARNINGS": false, "COMPILER_DISABLE_GCC13_WARNINGS": false, "COMPILER_DUMP_RTL_FILES": false, "COMPILER_RT_LIB_GCCLIB": true, "COMPILER_RT_LIB_NAME": "gcc", "COMPILER_ORPHAN_SECTIONS_WARNING": false, "COMPILER_ORPHAN_SECTIONS_PLACE": true, "APPTRACE_DEST_JTAG": false, "APPTRACE_DEST_NONE": true, "APPTRACE_DEST_UART1": false, "APPTRACE_DEST_USB_CDC": false, "APPTRACE_DEST_UART_NONE": true, "APPTRACE_UART_TASK_PRIO": 1, "APPTRACE_LOCK_ENABLE": true, "BT_ENABLED": true, "BT_BLUEDROID_ENABLED": true, "BT_NIMBLE_ENABLED": false, "BT_CONTROLLER_ONLY": false, "BT_CONTROLLER_ENABLED": true, "BT_CONTROLLER_DISABLED": false, "BT_BTC_TASK_STACK_SIZE": 3072, "BT_BLUEDROID_PINNED_TO_CORE": 0, "BT_BTU_TASK_STACK_SIZE": 4352, "BT_BLUEDROID_MEM_DEBUG": false, "BT_BLUEDROID_ESP_COEX_VSC": true, "BT_BLE_ENABLED": true, "BT_GATTS_ENABLE": true, "BT_GATTS_PPCP_CHAR_GAP": false, "BT_BLE_BLUFI_ENABLE": false, "BT_GATT_MAX_SR_PROFILES": 8, "BT_GATT_MAX_SR_ATTRIBUTES": 100, "BT_GATTS_SEND_SERVICE_CHANGE_MANUAL": false, "BT_GATTS_SEND_SERVICE_CHANGE_AUTO": true, "BT_GATTS_SEND_SERVICE_CHANGE_MODE": 0, "BT_GATTS_ROBUST_CACHING_ENABLED": false, "BT_GATTS_DEVICE_NAME_WRITABLE": false, "BT_GATTS_APPEARANCE_WRITABLE": false, "BT_GATTC_ENABLE": true, "BT_GATTC_MAX_CACHE_CHAR": 40, "BT_GATTC_NOTIF_REG_MAX": 5, "BT_GATTC_CACHE_NVS_FLASH": false, "BT_GATTC_CONNECT_RETRY_COUNT": 3, "BT_BLE_ESTAB_LINK_CONN_TOUT": 30, "BT_BLE_SMP_ENABLE": true, "BT_SMP_SLAVE_CON_PARAMS_UPD_ENABLE": false, "BT_BLE_SMP_ID_RESET_ENABLE": false, "BT_BLE_SMP_BOND_NVS_FLASH": true, "BT_STACK_NO_LOG": false, "BT_LOG_HCI_TRACE_LEVEL_NONE": false, "BT_LOG_HCI_TRACE_LEVEL_ERROR": false, "BT_LOG_HCI_TRACE_LEVEL_WARNING": true, "BT_LOG_HCI_TRACE_LEVEL_API": false, "BT_LOG_HCI_TRACE_LEVEL_EVENT": false, "BT_LOG_HCI_TRACE_LEVEL_DEBUG": false, "BT_LOG_HCI_TRACE_LEVEL_VERBOSE": false, "BT_LOG_HCI_TRACE_LEVEL": 2, "BT_LOG_BTM_TRACE_LEVEL_NONE": false, "BT_LOG_BTM_TRACE_LEVEL_ERROR": false, "BT_LOG_BTM_TRACE_LEVEL_WARNING": true, "BT_LOG_BTM_TRACE_LEVEL_API": false, "BT_LOG_BTM_TRACE_LEVEL_EVENT": false, "BT_LOG_BTM_TRACE_LEVEL_DEBUG": false, "BT_LOG_BTM_TRACE_LEVEL_VERBOSE": false, "BT_LOG_BTM_TRACE_LEVEL": 2, "BT_LOG_L2CAP_TRACE_LEVEL_NONE": false, "BT_LOG_L2CAP_TRACE_LEVEL_ERROR": false, "BT_LOG_L2CAP_TRACE_LEVEL_WARNING": true, "BT_LOG_L2CAP_TRACE_LEVEL_API": false, "BT_LOG_L2CAP_TRACE_LEVEL_EVENT": false, "BT_LOG_L2CAP_TRACE_LEVEL_DEBUG": false, "BT_LOG_L2CAP_TRACE_LEVEL_VERBOSE": false, "BT_LOG_L2CAP_TRACE_LEVEL": 2, "BT_LOG_RFCOMM_TRACE_LEVEL_NONE": false, "BT_LOG_RFCOMM_TRACE_LEVEL_ERROR": false, "BT_LOG_RFCOMM_TRACE_LEVEL_WARNING": true, "BT_LOG_RFCOMM_TRACE_LEVEL_API": false, "BT_LOG_RFCOMM_TRACE_LEVEL_EVENT": false, "BT_LOG_RFCOMM_TRACE_LEVEL_DEBUG": false, "BT_LOG_RFCOMM_TRACE_LEVEL_VERBOSE": false, "BT_LOG_RFCOMM_TRACE_LEVEL": 2, "BT_LOG_SDP_TRACE_LEVEL_NONE": false, "BT_LOG_SDP_TRACE_LEVEL_ERROR": false, "BT_LOG_SDP_TRACE_LEVEL_WARNING": true, "BT_LOG_SDP_TRACE_LEVEL_API": false, "BT_LOG_SDP_TRACE_LEVEL_EVENT": false, "BT_LOG_SDP_TRACE_LEVEL_DEBUG": false, "BT_LOG_SDP_TRACE_LEVEL_VERBOSE": false, "BT_LOG_SDP_TRACE_LEVEL": 2, "BT_LOG_GAP_TRACE_LEVEL_NONE": false, "BT_LOG_GAP_TRACE_LEVEL_ERROR": false, "BT_LOG_GAP_TRACE_LEVEL_WARNING": true, "BT_LOG_GAP_TRACE_LEVEL_API": false, "BT_LOG_GAP_TRACE_LEVEL_EVENT": false, "BT_LOG_GAP_TRACE_LEVEL_DEBUG": false, "BT_LOG_GAP_TRACE_LEVEL_VERBOSE": false, "BT_LOG_GAP_TRACE_LEVEL": 2, "BT_LOG_BNEP_TRACE_LEVEL_NONE": false, "BT_LOG_BNEP_TRACE_LEVEL_ERROR": false, "BT_LOG_BNEP_TRACE_LEVEL_WARNING": true, "BT_LOG_BNEP_TRACE_LEVEL_API": false, "BT_LOG_BNEP_TRACE_LEVEL_EVENT": false, "BT_LOG_BNEP_TRACE_LEVEL_DEBUG": false, "BT_LOG_BNEP_TRACE_LEVEL_VERBOSE": false, "BT_LOG_BNEP_TRACE_LEVEL": 2, "BT_LOG_PAN_TRACE_LEVEL_NONE": false, "BT_LOG_PAN_TRACE_LEVEL_ERROR": false, "BT_LOG_PAN_TRACE_LEVEL_WARNING": true, "BT_LOG_PAN_TRACE_LEVEL_API": false, "BT_LOG_PAN_TRACE_LEVEL_EVENT": false, "BT_LOG_PAN_TRACE_LEVEL_DEBUG": false, "BT_LOG_PAN_TRACE_LEVEL_VERBOSE": false, "BT_LOG_PAN_TRACE_LEVEL": 2, "BT_LOG_A2D_TRACE_LEVEL_NONE": false, "BT_LOG_A2D_TRACE_LEVEL_ERROR": false, "BT_LOG_A2D_TRACE_LEVEL_WARNING": true, "BT_LOG_A2D_TRACE_LEVEL_API": false, "BT_LOG_A2D_TRACE_LEVEL_EVENT": false, "BT_LOG_A2D_TRACE_LEVEL_DEBUG": false, "BT_LOG_A2D_TRACE_LEVEL_VERBOSE": false, "BT_LOG_A2D_TRACE_LEVEL": 2, "BT_LOG_AVDT_TRACE_LEVEL_NONE": false, "BT_LOG_AVDT_TRACE_LEVEL_ERROR": false, "BT_LOG_AVDT_TRACE_LEVEL_WARNING": true, "BT_LOG_AVDT_TRACE_LEVEL_API": false, "BT_LOG_AVDT_TRACE_LEVEL_EVENT": false, "BT_LOG_AVDT_TRACE_LEVEL_DEBUG": false, "BT_LOG_AVDT_TRACE_LEVEL_VERBOSE": false, "BT_LOG_AVDT_TRACE_LEVEL": 2, "BT_LOG_AVCT_TRACE_LEVEL_NONE": false, "BT_LOG_AVCT_TRACE_LEVEL_ERROR": false, "BT_LOG_AVCT_TRACE_LEVEL_WARNING": true, "BT_LOG_AVCT_TRACE_LEVEL_API": false, "BT_LOG_AVCT_TRACE_LEVEL_EVENT": false, "BT_LOG_AVCT_TRACE_LEVEL_DEBUG": false, "BT_LOG_AVCT_TRACE_LEVEL_VERBOSE": false, "BT_LOG_AVCT_TRACE_LEVEL": 2, "BT_LOG_AVRC_TRACE_LEVEL_NONE": false, "BT_LOG_AVRC_TRACE_LEVEL_ERROR": false, "BT_LOG_AVRC_TRACE_LEVEL_WARNING": true, "BT_LOG_AVRC_TRACE_LEVEL_API": false, "BT_LOG_AVRC_TRACE_LEVEL_EVENT": false, "BT_LOG_AVRC_TRACE_LEVEL_DEBUG": false, "BT_LOG_AVRC_TRACE_LEVEL_VERBOSE": false, "BT_LOG_AVRC_TRACE_LEVEL": 2, "BT_LOG_MCA_TRACE_LEVEL_NONE": false, "BT_LOG_MCA_TRACE_LEVEL_ERROR": false, "BT_LOG_MCA_TRACE_LEVEL_WARNING": true, "BT_LOG_MCA_TRACE_LEVEL_API": false, "BT_LOG_MCA_TRACE_LEVEL_EVENT": false, "BT_LOG_MCA_TRACE_LEVEL_DEBUG": false, "BT_LOG_MCA_TRACE_LEVEL_VERBOSE": false, "BT_LOG_MCA_TRACE_LEVEL": 2, "BT_LOG_HID_TRACE_LEVEL_NONE": false, "BT_LOG_HID_TRACE_LEVEL_ERROR": false, "BT_LOG_HID_TRACE_LEVEL_WARNING": true, "BT_LOG_HID_TRACE_LEVEL_API": false, "BT_LOG_HID_TRACE_LEVEL_EVENT": false, "BT_LOG_HID_TRACE_LEVEL_DEBUG": false, "BT_LOG_HID_TRACE_LEVEL_VERBOSE": false, "BT_LOG_HID_TRACE_LEVEL": 2, "BT_LOG_APPL_TRACE_LEVEL_NONE": false, "BT_LOG_APPL_TRACE_LEVEL_ERROR": false, "BT_LOG_APPL_TRACE_LEVEL_WARNING": true, "BT_LOG_APPL_TRACE_LEVEL_API": false, "BT_LOG_APPL_TRACE_LEVEL_EVENT": false, "BT_LOG_APPL_TRACE_LEVEL_DEBUG": false, "BT_LOG_APPL_TRACE_LEVEL_VERBOSE": false, "BT_LOG_APPL_TRACE_LEVEL": 2, "BT_LOG_GATT_TRACE_LEVEL_NONE": false, "BT_LOG_GATT_TRACE_LEVEL_ERROR": false, "BT_LOG_GATT_TRACE_LEVEL_WARNING": true, "BT_LOG_GATT_TRACE_LEVEL_API": false, "BT_LOG_GATT_TRACE_LEVEL_EVENT": false, "BT_LOG_GATT_TRACE_LEVEL_DEBUG": false, "BT_LOG_GATT_TRACE_LEVEL_VERBOSE": false, "BT_LOG_GATT_TRACE_LEVEL": 2, "BT_LOG_SMP_TRACE_LEVEL_NONE": false, "BT_LOG_SMP_TRACE_LEVEL_ERROR": false, "BT_LOG_SMP_TRACE_LEVEL_WARNING": true, "BT_LOG_SMP_TRACE_LEVEL_API": false, "BT_LOG_SMP_TRACE_LEVEL_EVENT": false, "BT_LOG_SMP_TRACE_LEVEL_DEBUG": false, "BT_LOG_SMP_TRACE_LEVEL_VERBOSE": false, "BT_LOG_SMP_TRACE_LEVEL": 2, "BT_LOG_BTIF_TRACE_LEVEL_NONE": false, "BT_LOG_BTIF_TRACE_LEVEL_ERROR": false, "BT_LOG_BTIF_TRACE_LEVEL_WARNING": true, "BT_LOG_BTIF_TRACE_LEVEL_API": false, "BT_LOG_BTIF_TRACE_LEVEL_EVENT": false, "BT_LOG_BTIF_TRACE_LEVEL_DEBUG": false, "BT_LOG_BTIF_TRACE_LEVEL_VERBOSE": false, "BT_LOG_BTIF_TRACE_LEVEL": 2, "BT_LOG_BTC_TRACE_LEVEL_NONE": false, "BT_LOG_BTC_TRACE_LEVEL_ERROR": false, "BT_LOG_BTC_TRACE_LEVEL_WARNING": true, "BT_LOG_BTC_TRACE_LEVEL_API": false, "BT_LOG_BTC_TRACE_LEVEL_EVENT": false, "BT_LOG_BTC_TRACE_LEVEL_DEBUG": false, "BT_LOG_BTC_TRACE_LEVEL_VERBOSE": false, "BT_LOG_BTC_TRACE_LEVEL": 2, "BT_LOG_OSI_TRACE_LEVEL_NONE": false, "BT_LOG_OSI_TRACE_LEVEL_ERROR": false, "BT_LOG_OSI_TRACE_LEVEL_WARNING": true, "BT_LOG_OSI_TRACE_LEVEL_API": false, "BT_LOG_OSI_TRACE_LEVEL_EVENT": false, "BT_LOG_OSI_TRACE_LEVEL_DEBUG": false, "BT_LOG_OSI_TRACE_LEVEL_VERBOSE": false, "BT_LOG_OSI_TRACE_LEVEL": 2, "BT_LOG_BLUFI_TRACE_LEVEL_NONE": false, "BT_LOG_BLUFI_TRACE_LEVEL_ERROR": false, "BT_LOG_BLUFI_TRACE_LEVEL_WARNING": true, "BT_LOG_BLUFI_TRACE_LEVEL_API": false, "BT_LOG_BLUFI_TRACE_LEVEL_EVENT": false, "BT_LOG_BLUFI_TRACE_LEVEL_DEBUG": false, "BT_LOG_BLUFI_TRACE_LEVEL_VERBOSE": false, "BT_LOG_BLUFI_TRACE_LEVEL": 2, "BT_ACL_CONNECTIONS": 4, "BT_MULTI_CONNECTION_ENBALE": true, "BT_ALLOCATION_FROM_SPIRAM_FIRST": false, "BT_BLE_DYNAMIC_ENV_MEMORY": false, "BT_SMP_ENABLE": true, "BT_SMP_MAX_BONDS": 15, "BT_BLE_ACT_SCAN_REP_ADV_SCAN": false, "BT_MAX_DEVICE_NAME_LEN": 32, "BT_BLE_RPA_TIMEOUT": 900, "BT_BLE_50_FEATURES_SUPPORTED": false, "BT_BLE_42_FEATURES_SUPPORTED": true, "BT_BLE_42_DTM_TEST_EN": true, "BT_BLE_42_ADV_EN": true, "BT_BLE_42_SCAN_EN": true, "BT_BLE_HIGH_DUTY_ADV_INTERVAL": false, "BT_ABORT_WHEN_ALLOCATION_FAILS": false, "BT_CTRL_MODE_EFF": 1, "BT_CTRL_BLE_MAX_ACT": 6, "BT_CTRL_BLE_MAX_ACT_EFF": 6, "BT_CTRL_BLE_STATIC_ACL_TX_BUF_NB": 0, "BT_CTRL_PINNED_TO_CORE": 0, "BT_CTRL_HCI_MODE_VHCI": true, "BT_CTRL_HCI_MODE_UART_H4": false, "BT_CTRL_HCI_TL": 1, "BT_CTRL_ADV_DUP_FILT_MAX": 30, "BT_BLE_CCA_MODE_NONE": true, "BT_BLE_CCA_MODE_HW": false, "BT_BLE_CCA_MODE_SW": false, "BT_BLE_CCA_MODE": 0, "BT_CTRL_HW_CCA_VAL": 20, "BT_CTRL_HW_CCA_EFF": 0, "BT_CTRL_CE_LENGTH_TYPE_ORIG": true, "BT_CTRL_CE_LENGTH_TYPE_CE": false, "BT_CTRL_CE_LENGTH_TYPE_SD": false, "BT_CTRL_CE_LENGTH_TYPE_EFF": 0, "BT_CTRL_TX_ANTENNA_INDEX_0": true, "BT_CTRL_TX_ANTENNA_INDEX_1": false, "BT_CTRL_TX_ANTENNA_INDEX_EFF": 0, "BT_CTRL_RX_ANTENNA_INDEX_0": true, "BT_CTRL_RX_ANTENNA_INDEX_1": false, "BT_CTRL_RX_ANTENNA_INDEX_EFF": 0, "BT_CTRL_DFT_TX_POWER_LEVEL_N24": false, "BT_CTRL_DFT_TX_POWER_LEVEL_N21": false, "BT_CTRL_DFT_TX_POWER_LEVEL_N18": false, "BT_CTRL_DFT_TX_POWER_LEVEL_N15": false, "BT_CTRL_DFT_TX_POWER_LEVEL_N12": false, "BT_CTRL_DFT_TX_POWER_LEVEL_N9": false, "BT_CTRL_DFT_TX_POWER_LEVEL_N6": false, "BT_CTRL_DFT_TX_POWER_LEVEL_N3": false, "BT_CTRL_DFT_TX_POWER_LEVEL_N0": false, "BT_CTRL_DFT_TX_POWER_LEVEL_P3": false, "BT_CTRL_DFT_TX_POWER_LEVEL_P6": false, "BT_CTRL_DFT_TX_POWER_LEVEL_P9": true, "BT_CTRL_DFT_TX_POWER_LEVEL_P12": false, "BT_CTRL_DFT_TX_POWER_LEVEL_P15": false, "BT_CTRL_DFT_TX_POWER_LEVEL_P18": false, "BT_CTRL_DFT_TX_POWER_LEVEL_P20": false, "BT_CTRL_DFT_TX_POWER_LEVEL_EFF": 11, "BT_CTRL_BLE_ADV_REPORT_FLOW_CTRL_SUPP": true, "BT_CTRL_BLE_ADV_REPORT_FLOW_CTRL_NUM": 100, "BT_CTRL_BLE_ADV_REPORT_DISCARD_THRSHOLD": 20, "BT_CTRL_BLE_SCAN_DUPL": true, "BT_CTRL_SCAN_DUPL_TYPE_DEVICE": true, "BT_CTRL_SCAN_DUPL_TYPE_DATA": false, "BT_CTRL_SCAN_DUPL_TYPE_DATA_DEVICE": false, "BT_CTRL_SCAN_DUPL_TYPE": 0, "BT_CTRL_SCAN_DUPL_CACHE_SIZE": 100, "BT_CTRL_DUPL_SCAN_CACHE_REFRESH_PERIOD": 0, "BT_CTRL_BLE_MESH_SCAN_DUPL_EN": false, "BT_CTRL_COEX_PHY_CODED_TX_RX_TLIM_EN": false, "BT_CTRL_COEX_PHY_CODED_TX_RX_TLIM_DIS": true, "BT_CTRL_COEX_PHY_CODED_TX_RX_TLIM_EFF": 0, "BT_CTRL_MODEM_SLEEP": false, "BT_CTRL_SLEEP_MODE_EFF": 0, "BT_CTRL_SLEEP_CLOCK_EFF": 0, "BT_CTRL_HCI_TL_EFF": 1, "BT_CTRL_AGC_RECORRECT_EN": false, "BT_CTRL_SCAN_BACKOFF_UPPERLIMITMAX": false, "BT_BLE_ADV_DATA_LENGTH_ZERO_AUX": false, "BT_CTRL_CHAN_ASS_EN": true, "BT_CTRL_LE_PING_EN": true, "BT_CTRL_BLE_LLCP_CONN_UPDATE": false, "BT_CTRL_BLE_LLCP_CHAN_MAP_UPDATE": false, "BT_CTRL_BLE_LLCP_PHY_UPDATE": false, "BT_CTRL_RUN_IN_FLASH_ONLY": false, "BT_CTRL_CHECK_CONNECT_IND_ACCESS_ADDRESS": false, "BT_ALARM_MAX_NUM": 50, "BT_BLE_LOG_SPI_OUT_ENABLED": false, "BT_HCI_LOG_DEBUG_EN": false, "BLE_MESH": false, "CONSOLE_SORTED_HELP": false, "TWAI_ISR_IN_IRAM": false, "TWAI_ERRATA_FIX_LISTEN_ONLY_DOM": true, "ADC_SUPPRESS_DEPRECATE_WARN": false, "ADC_SKIP_LEGACY_CONFLICT_CHECK": false, "ADC_CALI_SUPPRESS_DEPRECATE_WARN": false, "GPTIMER_SUPPRESS_DEPRECATE_WARN": false, "GPTIMER_SKIP_LEGACY_CONFLICT_CHECK": false, "RMT_SUPPRESS_DEPRECATE_WARN": false, "RMT_SKIP_LEGACY_CONFLICT_CHECK": false, "I2S_SUPPRESS_DEPRECATE_WARN": false, "I2S_SKIP_LEGACY_CONFLICT_CHECK": false, "SDM_SUPPRESS_DEPRECATE_WARN": false, "SDM_SKIP_LEGACY_CONFLICT_CHECK": false, "TEMP_SENSOR_SUPPRESS_DEPRECATE_WARN": false, "TEMP_SENSOR_SKIP_LEGACY_CONFLICT_CHECK": false, "EFUSE_CUSTOM_TABLE": false, "EFUSE_VIRTUAL": false, "EFUSE_MAX_BLK_LEN": 256, "ESP_TLS_USING_MBEDTLS": true, "ESP_TLS_USE_DS_PERIPHERAL": true, "ESP_TLS_CLIENT_SESSION_TICKETS": false, "ESP_TLS_SERVER_SESSION_TICKETS": false, "ESP_TLS_SERVER_CERT_SELECT_HOOK": false, "ESP_TLS_SERVER_MIN_AUTH_MODE_OPTIONAL": false, "ESP_TLS_PSK_VERIFICATION": false, "ESP_TLS_INSECURE": false, "ADC_ONESHOT_CTRL_FUNC_IN_IRAM": false, "ADC_CONTINUOUS_ISR_IRAM_SAFE": false, "ADC_CONTINUOUS_FORCE_USE_ADC2_ON_C3_S3": false, "ADC_ONESHOT_FORCE_USE_ADC2_ON_C3": false, "ADC_ENABLE_DEBUG_LOG": false, "ESP_COEX_ENABLED": true, "ESP_COEX_SW_COEXIST_ENABLE": true, "ESP_COEX_POWER_MANAGEMENT": false, "ESP_COEX_GPIO_DEBUG": false, "ESP_ERR_TO_NAME_LOOKUP": true, "GPIO_CTRL_FUNC_IN_IRAM": false, "GPTIMER_ISR_HANDLER_IN_IRAM": true, "GPTIMER_CTRL_FUNC_IN_IRAM": false, "GPTIMER_ISR_IRAM_SAFE": false, "GPTIMER_OBJ_CACHE_SAFE": true, "GPTIMER_ENABLE_DEBUG_LOG": false, "I2C_ISR_IRAM_SAFE": false, "I2C_ENABLE_DEBUG_LOG": false, "I2S_ISR_IRAM_SAFE": false, "I2S_ENABLE_DEBUG_LOG": false, "LEDC_CTRL_FUNC_IN_IRAM": false, "RMT_ISR_IRAM_SAFE": false, "RMT_RECV_FUNC_IN_IRAM": false, "RMT_ENABLE_DEBUG_LOG": false, "SDM_CTRL_FUNC_IN_IRAM": false, "SDM_ENABLE_DEBUG_LOG": false, "SPI_MASTER_IN_IRAM": false, "SPI_MASTER_ISR_IN_IRAM": true, "SPI_SLAVE_IN_IRAM": false, "SPI_SLAVE_ISR_IN_IRAM": true, "TEMP_SENSOR_ENABLE_DEBUG_LOG": false, "UART_ISR_IN_IRAM": false, "USJ_ENABLE_USB_SERIAL_JTAG": true, "ETH_ENABLED": true, "ETH_USE_SPI_ETHERNET": true, "ETH_SPI_ETHERNET_DM9051": false, "ETH_SPI_ETHERNET_W5500": false, "ETH_SPI_ETHERNET_KSZ8851SNL": false, "ETH_USE_OPENETH": false, "ETH_TRANSMIT_MUTEX": false, "ESP_EVENT_LOOP_PROFILING": false, "ESP_EVENT_POST_FROM_ISR": true, "ESP_EVENT_POST_FROM_IRAM_ISR": true, "ESP_GDBSTUB_ENABLED": true, "ESP_SYSTEM_GDBSTUB_RUNTIME": false, "ESP_GDBSTUB_SUPPORT_TASKS": true, "ESP_GDBSTUB_MAX_TASKS": 32, "ESPHID_TASK_SIZE_BT": 2048, "ESPHID_TASK_SIZE_BLE": 4096, "ESP_HTTP_CLIENT_ENABLE_HTTPS": true, "ESP_HTTP_CLIENT_ENABLE_BASIC_AUTH": false, "ESP_HTTP_CLIENT_ENABLE_DIGEST_AUTH": false, "ESP_HTTP_CLIENT_ENABLE_CUSTOM_TRANSPORT": false, "HTTPD_MAX_REQ_HDR_LEN": 512, "HTTPD_MAX_URI_LEN": 512, "HTTPD_ERR_RESP_NO_DELAY": true, "HTTPD_PURGE_BUF_LEN": 32, "HTTPD_LOG_PURGE_DATA": false, "HTTPD_WS_SUPPORT": false, "HTTPD_QUEUE_WORK_BLOCKING": false, "ESP_HTTPS_OTA_DECRYPT_CB": false, "ESP_HTTPS_OTA_ALLOW_HTTP": false, "ESP_HTTPS_SERVER_ENABLE": false, "ESP32C3_REV_MIN_0": false, "ESP32C3_REV_MIN_1": false, "ESP32C3_REV_MIN_2": false, "ESP32C3_REV_MIN_3": true, "ESP32C3_REV_MIN_4": false, "ESP32C3_REV_MIN_101": false, "ESP32C3_REV_MIN_FULL": 3, "ESP_REV_MIN_FULL": 3, "ESP32C3_REV_MAX_FULL": 199, "ESP_REV_MAX_FULL": 199, "ESP_EFUSE_BLOCK_REV_MIN_FULL": 0, "ESP_EFUSE_BLOCK_REV_MAX_FULL": 199, "ESP_MAC_ADDR_UNIVERSE_WIFI_STA": true, "ESP_MAC_ADDR_UNIVERSE_WIFI_AP": true, "ESP_MAC_ADDR_UNIVERSE_BT": true, "ESP_MAC_ADDR_UNIVERSE_ETH": true, "ESP_MAC_UNIVERSAL_MAC_ADDRESSES_FOUR": true, "ESP_MAC_UNIVERSAL_MAC_ADDRESSES": 4, "ESP32C3_UNIVERSAL_MAC_ADDRESSES_TWO": false, "ESP32C3_UNIVERSAL_MAC_ADDRESSES_FOUR": true, "ESP32C3_UNIVERSAL_MAC_ADDRESSES": 4, "ESP_MAC_USE_CUSTOM_MAC_AS_BASE_MAC": false, "ESP_SLEEP_POWER_DOWN_FLASH": false, "ESP_SLEEP_FLASH_LEAKAGE_WORKAROUND": true, "ESP_SLEEP_MSPI_NEED_ALL_IO_PU": false, "ESP_SLEEP_GPIO_RESET_WORKAROUND": true, "ESP_SLEEP_WAIT_FLASH_READY_EXTRA_DELAY": 0, "ESP_SLEEP_CACHE_SAFE_ASSERTION": false, "ESP_SLEEP_DEBUG": false, "ESP_SLEEP_GPIO_ENABLE_INTERNAL_RESISTORS": true, "RTC_CLK_SRC_INT_RC": true, "RTC_CLK_SRC_EXT_CRYS": false, "RTC_CLK_SRC_EXT_OSC": false, "RTC_CLK_SRC_INT_8MD256": false, "RTC_CLK_CAL_CYCLES": 1024, "PERIPH_CTRL_FUNC_IN_IRAM": true, "GDMA_CTRL_FUNC_IN_IRAM": true, "GDMA_ISR_IRAM_SAFE": false, "GDMA_ENABLE_DEBUG_LOG": false, "XTAL_FREQ_40": true, "XTAL_FREQ": 40, "ESP_SPI_BUS_LOCK_ISR_FUNCS_IN_IRAM": true, "LCD_ENABLE_DEBUG_LOG": false, "ESP_NETIF_IP_LOST_TIMER_INTERVAL": 120, "ESP_NETIF_TCPIP_LWIP": true, "ESP_NETIF_LOOPBACK": false, "ESP_NETIF_USES_TCPIP_WITH_BSD_API": true, "ESP_NETIF_RECEIVE_REPORT_ERRORS": false, "ESP_NETIF_L2_TAP": false, "ESP_NETIF_BRIDGE_EN": false, "ESP_NETIF_SET_DNS_PER_DEFAULT_NETIF": false, "ESP_PHY_ENABLED": true, "ESP_PHY_CALIBRATION_AND_DATA_STORAGE": true, "ESP_PHY_INIT_DATA_IN_PARTITION": false, "ESP_PHY_MAX_WIFI_TX_POWER": 20, "ESP_PHY_MAX_TX_POWER": 20, "ESP_PHY_REDUCE_TX_POWER": false, "ESP_PHY_ENABLE_USB": true, "ESP_PHY_ENABLE_CERT_TEST": false, "ESP_PHY_RF_CAL_PARTIAL": true, "ESP_PHY_RF_CAL_NONE": false, "ESP_PHY_RF_CAL_FULL": false, "ESP_PHY_CALIBRATION_MODE": 0, "ESP_PHY_PLL_TRACK_DEBUG": false, "ESP_PHY_RECORD_USED_TIME": false, "PM_ENABLE": false, "PM_SLP_IRAM_OPT": false, "PM_POWER_DOWN_CPU_IN_LIGHT_SLEEP": true, "RINGBUF_PLACE_FUNCTIONS_INTO_FLASH": false, "ESP_DEFAULT_CPU_FREQ_MHZ_80": false, "ESP_DEFAULT_CPU_FREQ_MHZ_160": true, "ESP_DEFAULT_CPU_FREQ_MHZ": 160, "ESP_SYSTEM_PANIC_PRINT_HALT": false, "ESP_SYSTEM_PANIC_PRINT_REBOOT": true, "ESP_SYSTEM_PANIC_SILENT_REBOOT": false, "ESP_SYSTEM_PANIC_GDBSTUB": false, "ESP_SYSTEM_PANIC_REBOOT_DELAY_SECONDS": 0, "ESP_SYSTEM_SINGLE_CORE_MODE": true, "ESP_SYSTEM_RTC_FAST_MEM_AS_HEAP_DEPCHECK": true, "ESP_SYSTEM_ALLOW_RTC_FAST_MEM_AS_HEAP": true, "ESP_SYSTEM_USE_EH_FRAME": false, "ESP_SYSTEM_MEMPROT_FEATURE": true, "ESP_SYSTEM_MEMPROT_FEATURE_LOCK": true, "ESP_SYSTEM_EVENT_QUEUE_SIZE": 32, "ESP_SYSTEM_EVENT_TASK_STACK_SIZE": 2304, "ESP_MAIN_TASK_STACK_SIZE": 3584, "ESP_MAIN_TASK_AFFINITY_CPU0": true, "ESP_MAIN_TASK_AFFINITY_NO_AFFINITY": false, "ESP_MAIN_TASK_AFFINITY": 0, "ESP_MINIMAL_SHARED_STACK_SIZE": 2048, "ESP_CONSOLE_UART_DEFAULT": true, "ESP_CONSOLE_USB_SERIAL_JTAG": false, "ESP_CONSOLE_UART_CUSTOM": false, "ESP_CONSOLE_NONE": false, "ESP_CONSOLE_SECONDARY_NONE": false, "ESP_CONSOLE_SECONDARY_USB_SERIAL_JTAG": true, "ESP_CONSOLE_USB_SERIAL_JTAG_ENABLED": true, "ESP_CONSOLE_UART": true, "ESP_CONSOLE_UART_NUM": 0, "ESP_CONSOLE_ROM_SERIAL_PORT_NUM": 0, "ESP_CONSOLE_UART_BAUDRATE": 115200, "ESP_INT_WDT": true, "ESP_INT_WDT_TIMEOUT_MS": 300, "ESP_TASK_WDT_EN": true, "ESP_TASK_WDT_INIT": true, "ESP_TASK_WDT_PANIC": false, "ESP_TASK_WDT_TIMEOUT_S": 5, "ESP_TASK_WDT_CHECK_IDLE_TASK_CPU0": true, "ESP_PANIC_HANDLER_IRAM": false, "ESP_DEBUG_STUBS_ENABLE": false, "ESP_DEBUG_OCDAWARE": true, "ESP_SYSTEM_CHECK_INT_LEVEL_4": true, "ESP_BROWNOUT_DET": true, "ESP_BROWNOUT_DET_LVL_SEL_7": true, "ESP_BROWNOUT_DET_LVL_SEL_6": false, "ESP_BROWNOUT_DET_LVL_SEL_5": false, "ESP_BROWNOUT_DET_LVL_SEL_4": false, "ESP_BROWNOUT_DET_LVL_SEL_3": false, "ESP_BROWNOUT_DET_LVL_SEL_2": false, "ESP_BROWNOUT_DET_LVL": 7, "ESP_SYSTEM_BROWNOUT_INTR": true, "ESP_SYSTEM_HW_STACK_GUARD": true, "ESP_SYSTEM_HW_PC_RECORD": true, "ESP_IPC_TASK_STACK_SIZE": 1024, "ESP_TIMER_PROFILING": false, "ESP_TIME_FUNCS_USE_RTC_TIMER": true, "ESP_TIME_FUNCS_USE_ESP_TIMER": true, "ESP_TIMER_TASK_STACK_SIZE": 3584, "ESP_TIMER_INTERRUPT_LEVEL": 1, "ESP_TIMER_SHOW_EXPERIMENTAL": false, "ESP_TIMER_TASK_AFFINITY": 0, "ESP_TIMER_TASK_AFFINITY_CPU0": true, "ESP_TIMER_ISR_AFFINITY_CPU0": true, "ESP_TIMER_SUPPORTS_ISR_DISPATCH_METHOD": false, "ESP_TIMER_IMPL_SYSTIMER": true, "ESP_WIFI_ENABLED": true, "ESP_WIFI_STATIC_RX_BUFFER_NUM": 10, "ESP_WIFI_DYNAMIC_RX_BUFFER_NUM": 32, "ESP_WIFI_STATIC_TX_BUFFER": false, "ESP_WIFI_DYNAMIC_TX_BUFFER": true, "ESP_WIFI_TX_BUFFER_TYPE": 1, "ESP_WIFI_DYNAMIC_TX_BUFFER_NUM": 32, "ESP_WIFI_STATIC_RX_MGMT_BUFFER": true, "ESP_WIFI_DYNAMIC_RX_MGMT_BUFFER": false, "ESP_WIFI_DYNAMIC_RX_MGMT_BUF": 0, "ESP_WIFI_RX_MGMT_BUF_NUM_DEF": 5, "ESP_WIFI_CSI_ENABLED": false, "ESP_WIFI_AMPDU_TX_ENABLED": true, "ESP_WIFI_TX_BA_WIN": 6, "ESP_WIFI_AMPDU_RX_ENABLED": true, "ESP_WIFI_RX_BA_WIN": 6, "ESP_WIFI_NVS_ENABLED": true, "ESP_WIFI_SOFTAP_BEACON_MAX_LEN": 752, "ESP_WIFI_MGMT_SBUF_NUM": 32, "ESP_WIFI_IRAM_OPT": true, "ESP_WIFI_EXTRA_IRAM_OPT": false, "ESP_WIFI_RX_IRAM_OPT": true, "ESP_WIFI_ENABLE_WPA3_SAE": true, "ESP_WIFI_ENABLE_SAE_PK": true, "ESP_WIFI_SOFTAP_SAE_SUPPORT": true, "ESP_WIFI_ENABLE_WPA3_OWE_STA": true, "ESP_WIFI_SLP_IRAM_OPT": false, "ESP_WIFI_SLP_DEFAULT_MIN_ACTIVE_TIME": 50, "ESP_WIFI_SLP_DEFAULT_MAX_ACTIVE_TIME": 10, "ESP_WIFI_SLP_DEFAULT_WAIT_BROADCAST_DATA_TIME": 15, "ESP_WIFI_FTM_ENABLE": false, "ESP_WIFI_STA_DISCONNECTED_PM_ENABLE": true, "ESP_WIFI_GCMP_SUPPORT": false, "ESP_WIFI_GMAC_SUPPORT": true, "ESP_WIFI_SOFTAP_SUPPORT": true, "ESP_WIFI_SLP_BEACON_LOST_OPT": false, "ESP_WIFI_ESPNOW_MAX_ENCRYPT_NUM": 7, "ESP_WIFI_MBEDTLS_CRYPTO": true, "ESP_WIFI_MBEDTLS_TLS_CLIENT": true, "ESP_WIFI_WAPI_PSK": false, "ESP_WIFI_SUITE_B_192": false, "ESP_WIFI_11KV_SUPPORT": false, "ESP_WIFI_MBO_SUPPORT": false, "ESP_WIFI_DPP_SUPPORT": false, "ESP_WIFI_11R_SUPPORT": false, "ESP_WIFI_WPS_SOFTAP_REGISTRAR": false, "ESP_WIFI_WPS_STRICT": false, "ESP_WIFI_WPS_PASSPHRASE": false, "ESP_WIFI_DEBUG_PRINT": false, "ESP_WIFI_TESTING_OPTIONS": false, "ESP_WIFI_ENTERPRISE_SUPPORT": true, "ESP_WIFI_ENT_FREE_DYNAMIC_BUFFER": false, "ESP_COREDUMP_ENABLE_TO_FLASH": false, "ESP_COREDUMP_ENABLE_TO_UART": false, "ESP_COREDUMP_ENABLE_TO_NONE": true, "FATFS_VOLUME_COUNT": 2, "FATFS_LFN_NONE": true, "FATFS_LFN_HEAP": false, "FATFS_LFN_STACK": false, "FATFS_SECTOR_512": false, "FATFS_SECTOR_4096": true, "FATFS_CODEPAGE_DYNAMIC": false, "FATFS_CODEPAGE_437": true, "FATFS_CODEPAGE_720": false, "FATFS_CODEPAGE_737": false, "FATFS_CODEPAGE_771": false, "FATFS_CODEPAGE_775": false, "FATFS_CODEPAGE_850": false, "FATFS_CODEPAGE_852": false, "FATFS_CODEPAGE_855": false, "FATFS_CODEPAGE_857": false, "FATFS_CODEPAGE_860": false, "FATFS_CODEPAGE_861": false, "FATFS_CODEPAGE_862": false, "FATFS_CODEPAGE_863": false, "FATFS_CODEPAGE_864": false, "FATFS_CODEPAGE_865": false, "FATFS_CODEPAGE_866": false, "FATFS_CODEPAGE_869": false, "FATFS_CODEPAGE_932": false, "FATFS_CODEPAGE_936": false, "FATFS_CODEPAGE_949": false, "FATFS_CODEPAGE_950": false, "FATFS_CODEPAGE": 437, "FATFS_FS_LOCK": 0, "FATFS_TIMEOUT_MS": 10000, "FATFS_PER_FILE_CACHE": true, "FATFS_USE_FASTSEEK": false, "FATFS_VFS_FSTAT_BLKSIZE": 0, "FATFS_IMMEDIATE_FSYNC": false, "FATFS_USE_LABEL": false, "FATFS_LINK_LOCK": true, "FREERTOS_SMP": false, "FREERTOS_UNICORE": true, "FREERTOS_HZ": 100, "FREERTOS_OPTIMIZED_SCHEDULER": true, "FREERTOS_CHECK_STACKOVERFLOW_NONE": false, "FREERTOS_CHECK_STACKOVERFLOW_PTRVAL": false, "FREERTOS_CHECK_STACKOVERFLOW_CANARY": true, "FREERTOS_THREAD_LOCAL_STORAGE_POINTERS": 1, "FREERTOS_IDLE_TASK_STACKSIZE": 1536, "FREERTOS_USE_IDLE_HOOK": false, "FREERTOS_USE_TICK_HOOK": false, "FREERTOS_MAX_TASK_NAME_LEN": 16, "FREERTOS_ENABLE_BACKWARD_COMPATIBILITY": false, "FREERTOS_TIMER_SERVICE_TASK_NAME": "Tmr Svc", "FREERTOS_TIMER_TASK_AFFINITY_CPU0": false, "FREERTOS_TIMER_TASK_NO_AFFINITY": true, "FREERTOS_TIMER_SERVICE_TASK_CORE_AFFINITY": 2147483647, "FREERTOS_TIMER_TASK_PRIORITY": 1, "FREERTOS_TIMER_TASK_STACK_DEPTH": 2048, "FREERTOS_TIMER_QUEUE_LENGTH": 10, "FREERTOS_QUEUE_REGISTRY_SIZE": 0, "FREERTOS_TASK_NOTIFICATION_ARRAY_ENTRIES": 1, "FREERTOS_USE_TRACE_FACILITY": false, "FREERTOS_USE_LIST_DATA_INTEGRITY_CHECK_BYTES": false, "FREERTOS_GENERATE_RUN_TIME_STATS": false, "FREERTOS_USE_APPLICATION_TASK_TAG": false, "FREERTOS_TASK_FUNCTION_WRAPPER": true, "FREERTOS_WATCHPOINT_END_OF_STACK": false, "FREERTOS_TLSP_DELETION_CALLBACKS": true, "FREERTOS_TASK_PRE_DELETION_HOOK": false, "FREERTOS_ENABLE_STATIC_TASK_CLEAN_UP": false, "FREERTOS_CHECK_MUTEX_GIVEN_BY_OWNER": true, "FREERTOS_ISR_STACKSIZE": 1536, "FREERTOS_INTERRUPT_BACKTRACE": true, "FREERTOS_TICK_SUPPORT_SYSTIMER": true, "FREERTOS_CORETIMER_SYSTIMER_LVL1": true, "FREERTOS_CORETIMER_SYSTIMER_LVL3": false, "FREERTOS_SYSTICK_USES_SYSTIMER": true, "FREERTOS_PLACE_FUNCTIONS_INTO_FLASH": false, "FREERTOS_CHECK_PORT_CRITICAL_COMPLIANCE": false, "FREERTOS_PORT": true, "FREERTOS_NO_AFFINITY": 2147483647, "FREERTOS_SUPPORT_STATIC_ALLOCATION": true, "FREERTOS_DEBUG_OCDAWARE": true, "FREERTOS_ENABLE_TASK_SNAPSHOT": true, "FREERTOS_PLACE_SNAPSHOT_FUNS_INTO_FLASH": true, "FREERTOS_NUMBER_OF_CORES": 1, "HAL_ASSERTION_EQUALS_SYSTEM": true, "HAL_ASSERTION_DISABLE": false, "HAL_ASSERTION_SILENT": false, "HAL_ASSERTION_ENABLE": false, "HAL_DEFAULT_ASSERTION_LEVEL": 2, "HAL_SPI_MASTER_FUNC_IN_IRAM": true, "HAL_SPI_SLAVE_FUNC_IN_IRAM": true, "HEAP_POISONING_DISABLED": true, "HEAP_POISONING_LIGHT": false, "HEAP_POISONING_COMPREHENSIVE": false, "HEAP_TRACING_OFF": true, "HEAP_TRACING_STANDALONE": false, "HEAP_TRACING_TOHOST": false, "HEAP_USE_HOOKS": false, "HEAP_TASK_TRACKING": false, "HEAP_ABORT_WHEN_ALLOCATION_FAILS": false, "HEAP_PLACE_FUNCTION_INTO_FLASH": false, "LOG_DEFAULT_LEVEL_NONE": false, "LOG_DEFAULT_LEVEL_ERROR": false, "LOG_DEFAULT_LEVEL_WARN": false, "LOG_DEFAULT_LEVEL_INFO": true, "LOG_DEFAULT_LEVEL_DEBUG": false, "LOG_DEFAULT_LEVEL_VERBOSE": false, "LOG_DEFAULT_LEVEL": 3, "LOG_MAXIMUM_EQUALS_DEFAULT": true, "LOG_MAXIMUM_LEVEL_DEBUG": false, "LOG_MAXIMUM_LEVEL_VERBOSE": false, "LOG_MAXIMUM_LEVEL": 3, "LOG_MASTER_LEVEL": false, "LOG_COLORS": true, "LOG_TIMESTAMP_SOURCE_RTOS": true, "LOG_TIMESTAMP_SOURCE_SYSTEM": false, "LWIP_ENABLE": true, "LWIP_LOCAL_HOSTNAME": "espressif", "LWIP_NETIF_API": false, "LWIP_TCPIP_TASK_PRIO": 18, "LWIP_TCPIP_CORE_LOCKING": false, "LWIP_CHECK_THREAD_SAFETY": false, "LWIP_DNS_SUPPORT_MDNS_QUERIES": true, "LWIP_L2_TO_L3_COPY": false, "LWIP_IRAM_OPTIMIZATION": false, "LWIP_EXTRA_IRAM_OPTIMIZATION": false, "LWIP_TIMERS_ONDEMAND": true, "LWIP_ND6": true, "LWIP_FORCE_ROUTER_FORWARDING": false, "LWIP_MAX_SOCKETS": 10, "LWIP_USE_ONLY_LWIP_SELECT": false, "LWIP_SO_LINGER": false, "LWIP_SO_REUSE": true, "LWIP_SO_REUSE_RXTOALL": true, "LWIP_SO_RCVBUF": false, "LWIP_NETBUF_RECVINFO": false, "LWIP_IP_DEFAULT_TTL": 64, "LWIP_IP4_FRAG": true, "LWIP_IP6_FRAG": true, "LWIP_IP4_REASSEMBLY": false, "LWIP_IP6_REASSEMBLY": false, "LWIP_IP_REASS_MAX_PBUFS": 10, "LWIP_IP_FORWARD": false, "LWIP_STATS": false, "LWIP_ESP_GRATUITOUS_ARP": true, "LWIP_GARP_TMR_INTERVAL": 60, "LWIP_ESP_MLDV6_REPORT": true, "LWIP_MLDV6_TMR_INTERVAL": 40, "LWIP_TCPIP_RECVMBOX_SIZE": 32, "LWIP_DHCP_DOES_ARP_CHECK": true, "LWIP_DHCP_DISABLE_CLIENT_ID": false, "LWIP_DHCP_DISABLE_VENDOR_CLASS_ID": true, "LWIP_DHCP_RESTORE_LAST_IP": false, "LWIP_DHCP_OPTIONS_LEN": 68, "LWIP_NUM_NETIF_CLIENT_DATA": 0, "LWIP_DHCP_COARSE_TIMER_SECS": 1, "LWIP_DHCPS": true, "LWIP_DHCPS_LEASE_UNIT": 60, "LWIP_DHCPS_MAX_STATION_NUM": 8, "LWIP_DHCPS_STATIC_ENTRIES": true, "LWIP_AUTOIP": false, "LWIP_IPV4": true, "LWIP_IPV6": true, "LWIP_IPV6_AUTOCONFIG": false, "LWIP_IPV6_NUM_ADDRESSES": 3, "LWIP_IPV6_FORWARD": false, "LWIP_NETIF_STATUS_CALLBACK": false, "LWIP_NETIF_LOOPBACK": true, "LWIP_LOOPBACK_MAX_PBUFS": 8, "LWIP_MAX_ACTIVE_TCP": 16, "LWIP_MAX_LISTENING_TCP": 16, "LWIP_TCP_HIGH_SPEED_RETRANSMISSION": true, "LWIP_TCP_MAXRTX": 12, "LWIP_TCP_SYNMAXRTX": 12, "LWIP_TCP_MSS": 1440, "LWIP_TCP_TMR_INTERVAL": 250, "LWIP_TCP_MSL": 60000, "LWIP_TCP_FIN_WAIT_TIMEOUT": 20000, "LWIP_TCP_SND_BUF_DEFAULT": 5760, "LWIP_TCP_WND_DEFAULT": 5760, "LWIP_TCP_RECVMBOX_SIZE": 6, "LWIP_TCP_ACCEPTMBOX_SIZE": 6, "LWIP_TCP_QUEUE_OOSEQ": true, "LWIP_TCP_OOSEQ_TIMEOUT": 6, "LWIP_TCP_OOSEQ_MAX_PBUFS": 4, "LWIP_TCP_SACK_OUT": false, "LWIP_TCP_OVERSIZE_MSS": true, "LWIP_TCP_OVERSIZE_QUARTER_MSS": false, "LWIP_TCP_OVERSIZE_DISABLE": false, "LWIP_TCP_RTO_TIME": 1500, "LWIP_MAX_UDP_PCBS": 16, "LWIP_UDP_RECVMBOX_SIZE": 6, "LWIP_CHECKSUM_CHECK_IP": false, "LWIP_CHECKSUM_CHECK_UDP": false, "LWIP_CHECKSUM_CHECK_ICMP": true, "LWIP_TCPIP_TASK_STACK_SIZE": 3072, "LWIP_TCPIP_TASK_AFFINITY_NO_AFFINITY": true, "LWIP_TCPIP_TASK_AFFINITY_CPU0": false, "LWIP_TCPIP_TASK_AFFINITY": 2147483647, "LWIP_IPV6_ND6_NUM_PREFIXES": 5, "LWIP_IPV6_ND6_NUM_ROUTERS": 3, "LWIP_IPV6_ND6_NUM_DESTINATIONS": 10, "LWIP_PPP_SUPPORT": false, "LWIP_IPV6_MEMP_NUM_ND6_QUEUE": 3, "LWIP_IPV6_ND6_NUM_NEIGHBORS": 5, "LWIP_SLIP_SUPPORT": false, "LWIP_ICMP": true, "LWIP_MULTICAST_PING": false, "LWIP_BROADCAST_PING": false, "LWIP_MAX_RAW_PCBS": 16, "LWIP_SNTP_MAX_SERVERS": 1, "LWIP_DHCP_GET_NTP_SRV": false, "LWIP_SNTP_UPDATE_DELAY": 3600000, "LWIP_SNTP_STARTUP_DELAY": true, "LWIP_SNTP_MAXIMUM_STARTUP_DELAY": 5000, "LWIP_DNS_MAX_HOST_IP": 1, "LWIP_DNS_MAX_SERVERS": 3, "LWIP_FALLBACK_DNS_SERVER_SUPPORT": false, "LWIP_DNS_SETSERVER_WITH_NETIF": false, "LWIP_BRIDGEIF_MAX_PORTS": 7, "LWIP_ESP_LWIP_ASSERT": true, "LWIP_HOOK_TCP_ISN_NONE": false, "LWIP_HOOK_TCP_ISN_DEFAULT": true, "LWIP_HOOK_TCP_ISN_CUSTOM": false, "LWIP_HOOK_IP6_ROUTE_NONE": true, "LWIP_HOOK_IP6_ROUTE_DEFAULT": false, "LWIP_HOOK_IP6_ROUTE_CUSTOM": false, "LWIP_HOOK_ND6_GET_GW_NONE": true, "LWIP_HOOK_ND6_GET_GW_DEFAULT": false, "LWIP_HOOK_ND6_GET_GW_CUSTOM": false, "LWIP_HOOK_IP6_SELECT_SRC_ADDR_NONE": true, "LWIP_HOOK_IP6_SELECT_SRC_ADDR_DEFAULT": false, "LWIP_HOOK_IP6_SELECT_SRC_ADDR_CUSTOM": false, "LWIP_HOOK_NETCONN_EXT_RESOLVE_NONE": true, "LWIP_HOOK_NETCONN_EXT_RESOLVE_DEFAULT": false, "LWIP_HOOK_NETCONN_EXT_RESOLVE_CUSTOM": false, "LWIP_HOOK_DNS_EXT_RESOLVE_NONE": true, "LWIP_HOOK_DNS_EXT_RESOLVE_CUSTOM": false, "LWIP_HOOK_IP6_INPUT_NONE": false, "LWIP_HOOK_IP6_INPUT_DEFAULT": true, "LWIP_HOOK_IP6_INPUT_CUSTOM": false, "LWIP_DEBUG": false, "MBEDTLS_INTERNAL_MEM_ALLOC": true, "MBEDTLS_DEFAULT_MEM_ALLOC": false, "MBEDTLS_CUSTOM_MEM_ALLOC": false, "MBEDTLS_ASYMMETRIC_CONTENT_LEN": true, "MBEDTLS_SSL_IN_CONTENT_LEN": 16384, "MBEDTLS_SSL_OUT_CONTENT_LEN": 4096, "MBEDTLS_DYNAMIC_BUFFER": false, "MBEDTLS_DEBUG": false, "MBEDTLS_SSL_PROTO_TLS1_3": false, "MBEDTLS_SSL_VARIABLE_BUFFER_LENGTH": false, "MBEDTLS_X509_TRUSTED_CERT_CALLBACK": false, "MBEDTLS_SSL_CONTEXT_SERIALIZATION": false, "MBEDTLS_SSL_KEEP_PEER_CERTIFICATE": true, "MBEDTLS_PKCS7_C": true, "MBEDTLS_CERTIFICATE_BUNDLE": true, "MBEDTLS_CERTIFICATE_BUNDLE_DEFAULT_FULL": true, "MBEDTLS_CERTIFICATE_BUNDLE_DEFAULT_CMN": false, "MBEDTLS_CERTIFICATE_BUNDLE_DEFAULT_NONE": false, "MBEDTLS_CUSTOM_CERTIFICATE_BUNDLE": false, "MBEDTLS_CERTIFICATE_BUNDLE_DEPRECATED_LIST": false, "MBEDTLS_CERTIFICATE_BUNDLE_MAX_CERTS": 200, "MBEDTLS_ECP_RESTARTABLE": false, "MBEDTLS_CMAC_C": true, "MBEDTLS_HARDWARE_AES": true, "MBEDTLS_AES_USE_INTERRUPT": true, "MBEDTLS_AES_INTERRUPT_LEVEL": 0, "MBEDTLS_GCM_SUPPORT_NON_AES_CIPHER": true, "MBEDTLS_HARDWARE_MPI": true, "MBEDTLS_LARGE_KEY_SOFTWARE_MPI": true, "MBEDTLS_MPI_USE_INTERRUPT": true, "MBEDTLS_MPI_INTERRUPT_LEVEL": 0, "MBEDTLS_HARDWARE_SHA": true, "MBEDTLS_ROM_MD5": true, "MBEDTLS_ATCA_HW_ECDSA_SIGN": false, "MBEDTLS_ATCA_HW_ECDSA_VERIFY": false, "MBEDTLS_HAVE_TIME": true, "MBEDTLS_PLATFORM_TIME_ALT": false, "MBEDTLS_HAVE_TIME_DATE": false, "MBEDTLS_ECDSA_DETERMINISTIC": true, "MBEDTLS_SHA512_C": true, "MBEDTLS_SHA3_C": false, "MBEDTLS_TLS_SERVER_AND_CLIENT": true, "MBEDTLS_TLS_SERVER_ONLY": false, "MBEDTLS_TLS_CLIENT_ONLY": false, "MBEDTLS_TLS_DISABLED": false, "MBEDTLS_TLS_SERVER": true, "MBEDTLS_TLS_CLIENT": true, "MBEDTLS_TLS_ENABLED": true, "MBEDTLS_PSK_MODES": false, "MBEDTLS_KEY_EXCHANGE_RSA": true, "MBEDTLS_KEY_EXCHANGE_ELLIPTIC_CURVE": true, "MBEDTLS_KEY_EXCHANGE_ECDHE_RSA": true, "MBEDTLS_KEY_EXCHANGE_ECDHE_ECDSA": true, "MBEDTLS_KEY_EXCHANGE_ECDH_ECDSA": true, "MBEDTLS_KEY_EXCHANGE_ECDH_RSA": true, "MBEDTLS_SSL_RENEGOTIATION": true, "MBEDTLS_SSL_PROTO_TLS1_2": true, "MBEDTLS_SSL_PROTO_GMTSSL1_1": false, "MBEDTLS_SSL_PROTO_DTLS": false, "MBEDTLS_SSL_ALPN": true, "MBEDTLS_CLIENT_SSL_SESSION_TICKETS": true, "MBEDTLS_SERVER_SSL_SESSION_TICKETS": true, "MBEDTLS_AES_C": true, "MBEDTLS_CAMELLIA_C": false, "MBEDTLS_DES_C": false, "MBEDTLS_BLOWFISH_C": false, "MBEDTLS_XTEA_C": false, "MBEDTLS_CCM_C": true, "MBEDTLS_GCM_C": true, "MBEDTLS_NIST_KW_C": false, "MBEDTLS_RIPEMD160_C": false, "MBEDTLS_PEM_PARSE_C": true, "MBEDTLS_PEM_WRITE_C": true, "MBEDTLS_X509_CRL_PARSE_C": true, "MBEDTLS_X509_CSR_PARSE_C": true, "MBEDTLS_ECP_C": true, "MBEDTLS_DHM_C": false, "MBEDTLS_ECDH_C": true, "MBEDTLS_ECDSA_C": true, "MBEDTLS_ECJPAKE_C": false, "MBEDTLS_ECP_DP_SECP192R1_ENABLED": true, "MBEDTLS_ECP_DP_SECP224R1_ENABLED": true, "MBEDTLS_ECP_DP_SECP256R1_ENABLED": true, "MBEDTLS_ECP_DP_SECP384R1_ENABLED": true, "MBEDTLS_ECP_DP_SECP521R1_ENABLED": true, "MBEDTLS_ECP_DP_SECP192K1_ENABLED": true, "MBEDTLS_ECP_DP_SECP224K1_ENABLED": true, "MBEDTLS_ECP_DP_SECP256K1_ENABLED": true, "MBEDTLS_ECP_DP_BP256R1_ENABLED": true, "MBEDTLS_ECP_DP_BP384R1_ENABLED": true, "MBEDTLS_ECP_DP_BP512R1_ENABLED": true, "MBEDTLS_ECP_DP_CURVE25519_ENABLED": true, "MBEDTLS_ECP_NIST_OPTIM": true, "MBEDTLS_ECP_FIXED_POINT_OPTIM": false, "MBEDTLS_POLY1305_C": false, "MBEDTLS_CHACHA20_C": false, "MBEDTLS_HKDF_C": false, "MBEDTLS_THREADING_C": false, "MBEDTLS_ERROR_STRINGS": true, "MBEDTLS_FS_IO": true, "MQTT_PROTOCOL_311": true, "MQTT_PROTOCOL_5": false, "MQTT_TRANSPORT_SSL": true, "MQTT_TRANSPORT_WEBSOCKET": true, "MQTT_TRANSPORT_WEBSOCKET_SECURE": true, "MQTT_MSG_ID_INCREMENTAL": false, "MQTT_SKIP_PUBLISH_IF_DISCONNECTED": false, "MQTT_REPORT_DELETED_MESSAGES": false, "MQTT_USE_CUSTOM_CONFIG": false, "MQTT_TASK_CORE_SELECTION_ENABLED": false, "MQTT_CUSTOM_OUTBOX": false, "NEWLIB_STDOUT_LINE_ENDING_CRLF": true, "NEWLIB_STDOUT_LINE_ENDING_LF": false, "NEWLIB_STDOUT_LINE_ENDING_CR": false, "NEWLIB_STDIN_LINE_ENDING_CRLF": false, "NEWLIB_STDIN_LINE_ENDING_LF": false, "NEWLIB_STDIN_LINE_ENDING_CR": true, "NEWLIB_NANO_FORMAT": false, "NEWLIB_TIME_SYSCALL_USE_RTC_HRT": true, "NEWLIB_TIME_SYSCALL_USE_RTC": false, "NEWLIB_TIME_SYSCALL_USE_HRT": false, "NEWLIB_TIME_SYSCALL_USE_NONE": false, "NVS_ENCRYPTION": false, "NVS_ASSERT_ERROR_CHECK": false, "NVS_LEGACY_DUP_KEYS_COMPATIBILITY": false, "OPENTHREAD_ENABLED": false, "OPENTHREAD_SPINEL_ONLY": false, "ESP_PROTOCOMM_SUPPORT_SECURITY_VERSION_0": true, "ESP_PROTOCOMM_SUPPORT_SECURITY_VERSION_1": true, "ESP_PROTOCOMM_SUPPORT_SECURITY_VERSION_2": true, "ESP_PROTOCOMM_SUPPORT_SECURITY_PATCH_VERSION": true, "PTHREAD_TASK_PRIO_DEFAULT": 5, "PTHREAD_TASK_STACK_SIZE_DEFAULT": 3072, "PTHREAD_STACK_MIN": 768, "PTHREAD_TASK_CORE_DEFAULT": -1, "PTHREAD_TASK_NAME_DEFAULT": "pthread", "MMU_PAGE_SIZE_64KB": true, "MMU_PAGE_MODE": "64KB", "MMU_PAGE_SIZE": 65536, "SPI_FLASH_BROWNOUT_RESET_XMC": true, "SPI_FLASH_BROWNOUT_RESET": true, "SPI_FLASH_SUSPEND_QVL_SUPPORTED": true, "SPI_FLASH_AUTO_SUSPEND": false, "SPI_FLASH_SUSPEND_TSUS_VAL_US": 50, "SPI_FLASH_FORCE_ENABLE_XMC_C_SUSPEND": false, "SPI_FLASH_VERIFY_WRITE": false, "SPI_FLASH_ENABLE_COUNTERS": false, "SPI_FLASH_ROM_DRIVER_PATCH": true, "SPI_FLASH_ROM_IMPL": false, "SPI_FLASH_DANGEROUS_WRITE_ABORTS": true, "SPI_FLASH_DANGEROUS_WRITE_FAILS": false, "SPI_FLASH_DANGEROUS_WRITE_ALLOWED": false, "SPI_FLASH_BYPASS_BLOCK_ERASE": false, "SPI_FLASH_YIELD_DURING_ERASE": true, "SPI_FLASH_ERASE_YIELD_DURATION_MS": 20, "SPI_FLASH_ERASE_YIELD_TICKS": 1, "SPI_FLASH_WRITE_CHUNK_SIZE": 8192, "SPI_FLASH_SIZE_OVERRIDE": false, "SPI_FLASH_CHECK_ERASE_TIMEOUT_DISABLED": false, "SPI_FLASH_OVERRIDE_CHIP_DRIVER_LIST": false, "SPI_FLASH_VENDOR_XMC_SUPPORTED": true, "SPI_FLASH_VENDOR_GD_SUPPORTED": true, "SPI_FLASH_VENDOR_ISSI_SUPPORTED": true, "SPI_FLASH_VENDOR_MXIC_SUPPORTED": true, "SPI_FLASH_VENDOR_WINBOND_SUPPORTED": true, "SPI_FLASH_VENDOR_BOYA_SUPPORTED": true, "SPI_FLASH_VENDOR_TH_SUPPORTED": true, "SPI_FLASH_SUPPORT_ISSI_CHIP": true, "SPI_FLASH_SUPPORT_MXIC_CHIP": true, "SPI_FLASH_SUPPORT_GD_CHIP": true, "SPI_FLASH_SUPPORT_WINBOND_CHIP": true, "SPI_FLASH_SUPPORT_BOYA_CHIP": true, "SPI_FLASH_SUPPORT_TH_CHIP": true, "SPI_FLASH_ENABLE_ENCRYPTED_READ_WRITE": true, "SPIFFS_MAX_PARTITIONS": 3, "SPIFFS_CACHE": true, "SPIFFS_CACHE_WR": true, "SPIFFS_CACHE_STATS": false, "SPIFFS_PAGE_CHECK": true, "SPIFFS_GC_MAX_RUNS": 10, "SPIFFS_GC_STATS": false, "SPIFFS_PAGE_SIZE": 256, "SPIFFS_OBJ_NAME_LEN": 32, "SPIFFS_FOLLOW_SYMLINKS": false, "SPIFFS_USE_MAGIC": true, "SPIFFS_USE_MAGIC_LENGTH": true, "SPIFFS_META_LENGTH": 4, "SPIFFS_USE_MTIME": true, "SPIFFS_DBG": false, "SPIFFS_API_DBG": false, "SPIFFS_GC_DBG": false, "SPIFFS_CACHE_DBG": false, "SPIFFS_CHECK_DBG": false, "SPIFFS_TEST_VISUALISATION": false, "WS_TRANSPORT": true, "WS_BUFFER_SIZE": 1024, "WS_DYNAMIC_BUFFER": false, "UNITY_ENABLE_FLOAT": true, "UNITY_ENABLE_DOUBLE": true, "UNITY_ENABLE_64BIT": false, "UNITY_ENABLE_COLOR": false, "UNITY_ENABLE_IDF_TEST_RUNNER": true, "UNITY_ENABLE_FIXTURE": false, "UNITY_ENABLE_BACKTRACE_ON_FAIL": false, "VFS_SUPPORT_IO": true, "VFS_SUPPORT_DIR": true, "VFS_SUPPORT_SELECT": true, "VFS_SUPPRESS_SELECT_DEBUG_OUTPUT": true, "VFS_SELECT_IN_RAM": false, "VFS_SUPPORT_TERMIOS": true, "VFS_MAX_COUNT": 8, "VFS_SEMIHOSTFS_MAX_MOUNT_POINTS": 1, "WL_SECTOR_SIZE_512": false, "WL_SECTOR_SIZE_4096": true, "WL_SECTOR_SIZE": 4096, "WIFI_PROV_SCAN_MAX_ENTRIES": 16, "WIFI_PROV_AUTOSTOP_TIMEOUT": 30, "WIFI_PROV_BLE_BONDING": false, "WIFI_PROV_BLE_FORCE_ENCRYPTION": false, "WIFI_PROV_BLE_NOTIFY": false, "WIFI_PROV_KEEP_BLE_ON_AFTER_PROV": false, "WIFI_PROV_STA_ALL_CHANNEL_SCAN": true, "WIFI_PROV_STA_FAST_SCAN": false, "IDF_EXPERIMENTAL_FEATURES": false}, "ranges": {"BOOTLOADER_PROJECT_VER": [0, 4294967295], "BOOTLOADER_WDT_TIME_MS": [0, 120000], "APP_RETRIEVE_LEN_ELF_SHA": [8, 64], "APPTRACE_UART_TASK_PRIO": [1, 32], "BT_GATT_MAX_SR_PROFILES": [1, 32], "BT_GATT_MAX_SR_ATTRIBUTES": [1, 500], "BT_GATTC_MAX_CACHE_CHAR": [1, 500], "BT_GATTC_NOTIF_REG_MAX": [1, 64], "BT_GATTC_CONNECT_RETRY_COUNT": [0, 255], "BT_BLE_ESTAB_LINK_CONN_TOUT": [1, 60], "BT_ACL_CONNECTIONS": [1, 9], "BT_SMP_MAX_BONDS": [1, 32], "BT_MAX_DEVICE_NAME_LEN": [32, 248], "BT_BLE_RPA_TIMEOUT": [1, 3600], "BT_CTRL_BLE_MAX_ACT": [1, 10], "BT_CTRL_BLE_STATIC_ACL_TX_BUF_NB": [0, 12], "BT_CTRL_ADV_DUP_FILT_MAX": [1, 500], "BT_CTRL_HW_CCA_VAL": [20, 100], "BT_CTRL_BLE_ADV_REPORT_FLOW_CTRL_NUM": [50, 1000], "BT_CTRL_BLE_ADV_REPORT_DISCARD_THRSHOLD": [1, 1000], "BT_CTRL_SCAN_DUPL_CACHE_SIZE": [10, 1000], "BT_CTRL_DUPL_SCAN_CACHE_REFRESH_PERIOD": [0, 1000], "ESPHID_TASK_SIZE_BT": [2048, 10240], "ESPHID_TASK_SIZE_BLE": [2048, 10240], "ESP_SLEEP_WAIT_FLASH_READY_EXTRA_DELAY": [0, 5000], "RTC_CLK_CAL_CYCLES": [0, 32766], "ESP_NETIF_IP_LOST_TIMER_INTERVAL": [0, 65535], "ESP_PHY_MAX_WIFI_TX_POWER": [10, 20], "ESP_SYSTEM_PANIC_REBOOT_DELAY_SECONDS": [0, 99], "ESP_CONSOLE_UART_BAUDRATE": [1200, 4000000], "ESP_INT_WDT_TIMEOUT_MS": [10, 10000], "ESP_TASK_WDT_TIMEOUT_S": [1, 60], "ESP_IPC_TASK_STACK_SIZE": [512, 65536], "ESP_TIMER_TASK_STACK_SIZE": [2048, 65536], "ESP_TIMER_INTERRUPT_LEVEL": [1, 1], "ESP_WIFI_STATIC_RX_BUFFER_NUM": [2, 25], "ESP_WIFI_DYNAMIC_RX_BUFFER_NUM": [0, 128], "ESP_WIFI_DYNAMIC_TX_BUFFER_NUM": [1, 128], "ESP_WIFI_RX_MGMT_BUF_NUM_DEF": [1, 10], "ESP_WIFI_TX_BA_WIN": [2, 32], "ESP_WIFI_RX_BA_WIN": [2, 32], "ESP_WIFI_SOFTAP_BEACON_MAX_LEN": [752, 1256], "ESP_WIFI_MGMT_SBUF_NUM": [6, 32], "ESP_WIFI_SLP_DEFAULT_MIN_ACTIVE_TIME": [8, 60], "ESP_WIFI_SLP_DEFAULT_MAX_ACTIVE_TIME": [10, 60], "ESP_WIFI_SLP_DEFAULT_WAIT_BROADCAST_DATA_TIME": [10, 30], "ESP_WIFI_ESPNOW_MAX_ENCRYPT_NUM": [0, 17], "FATFS_VOLUME_COUNT": [1, 10], "FATFS_FS_LOCK": [0, 65535], "FREERTOS_HZ": [1, 1000], "FREERTOS_THREAD_LOCAL_STORAGE_POINTERS": [1, 256], "FREERTOS_IDLE_TASK_STACKSIZE": [768, 32768], "FREERTOS_MAX_TASK_NAME_LEN": [1, 256], "FREERTOS_TIMER_TASK_PRIORITY": [1, 25], "FREERTOS_TIMER_TASK_STACK_DEPTH": [1536, 32768], "FREERTOS_TIMER_QUEUE_LENGTH": [5, 20], "FREERTOS_QUEUE_REGISTRY_SIZE": [0, 20], "FREERTOS_TASK_NOTIFICATION_ARRAY_ENTRIES": [1, 32], "FREERTOS_ISR_STACKSIZE": [1536, 32768], "FREERTOS_NUMBER_OF_CORES": [1, 2], "LWIP_TCPIP_TASK_PRIO": [1, 24], "LWIP_MAX_SOCKETS": [1, 253], "LWIP_IP_DEFAULT_TTL": [1, 255], "LWIP_IP_REASS_MAX_PBUFS": [10, 100], "LWIP_TCPIP_RECVMBOX_SIZE": [6, 64], "LWIP_DHCP_OPTIONS_LEN": [68, 255], "LWIP_NUM_NETIF_CLIENT_DATA": [0, 256], "LWIP_DHCP_COARSE_TIMER_SECS": [1, 10], "LWIP_DHCPS_LEASE_UNIT": [1, 3600], "LWIP_DHCPS_MAX_STATION_NUM": [1, 64], "LWIP_LOOPBACK_MAX_PBUFS": [0, 16], "LWIP_MAX_ACTIVE_TCP": [1, 1024], "LWIP_MAX_LISTENING_TCP": [1, 1024], "LWIP_TCP_MAXRTX": [3, 12], "LWIP_TCP_SYNMAXRTX": [3, 12], "LWIP_TCP_MSS": [536, 1460], "LWIP_TCP_SND_BUF_DEFAULT": [2440, 65535], "LWIP_TCP_WND_DEFAULT": [2440, 65535], "LWIP_TCP_RECVMBOX_SIZE": [6, 64], "LWIP_TCP_ACCEPTMBOX_SIZE": [1, 64], "LWIP_TCP_OOSEQ_TIMEOUT": [1, 30], "LWIP_TCP_OOSEQ_MAX_PBUFS": [0, 12], "LWIP_MAX_UDP_PCBS": [1, 1024], "LWIP_UDP_RECVMBOX_SIZE": [6, 64], "LWIP_TCPIP_TASK_STACK_SIZE": [2048, 65536], "LWIP_IPV6_MEMP_NUM_ND6_QUEUE": [3, 20], "LWIP_IPV6_ND6_NUM_NEIGHBORS": [3, 10], "LWIP_MAX_RAW_PCBS": [1, 1024], "LWIP_SNTP_MAX_SERVERS": [1, 16], "LWIP_SNTP_UPDATE_DELAY": [15000, 4294967295], "LWIP_SNTP_MAXIMUM_STARTUP_DELAY": [100, 300000], "LWIP_DNS_MAX_SERVERS": [1, 4], "LWIP_BRIDGEIF_MAX_PORTS": [1, 63], "MBEDTLS_SSL_IN_CONTENT_LEN": [512, 16384], "MBEDTLS_SSL_OUT_CONTENT_LEN": [512, 16384], "MBEDTLS_AES_INTERRUPT_LEVEL": [0, 3], "MBEDTLS_MPI_INTERRUPT_LEVEL": [0, 3], "PTHREAD_TASK_PRIO_DEFAULT": [0, 255], "SPI_FLASH_SUSPEND_TSUS_VAL_US": [20, 100], "SPI_FLASH_WRITE_CHUNK_SIZE": [256, 8192], "SPIFFS_MAX_PARTITIONS": [1, 10], "SPIFFS_GC_MAX_RUNS": [1, 10000], "SPIFFS_PAGE_SIZE": [256, 1024], "SPIFFS_OBJ_NAME_LEN": [1, 256], "VFS_MAX_COUNT": [1, 20], "WIFI_PROV_SCAN_MAX_ENTRIES": [1, 255], "WIFI_PROV_AUTOSTOP_TIMEOUT": [5, 600]}, "visible": {"SOC_ADC_SUPPORTED": false, "SOC_DEDICATED_GPIO_SUPPORTED": false, "SOC_UART_SUPPORTED": false, "SOC_GDMA_SUPPORTED": false, "SOC_AHB_GDMA_SUPPORTED": false, "SOC_GPTIMER_SUPPORTED": false, "SOC_TWAI_SUPPORTED": false, "SOC_BT_SUPPORTED": false, "SOC_ASYNC_MEMCPY_SUPPORTED": false, "SOC_USB_SERIAL_JTAG_SUPPORTED": false, "SOC_TEMP_SENSOR_SUPPORTED": false, "SOC_XT_WDT_SUPPORTED": false, "SOC_PHY_SUPPORTED": false, "SOC_WIFI_SUPPORTED": false, "SOC_SUPPORTS_SECURE_DL_MODE": false, "SOC_EFUSE_KEY_PURPOSE_FIELD": false, "SOC_EFUSE_HAS_EFUSE_RST_BUG": false, "SOC_EFUSE_SUPPORTED": false, "SOC_RTC_FAST_MEM_SUPPORTED": false, "SOC_RTC_MEM_SUPPORTED": false, "SOC_I2S_SUPPORTED": false, "SOC_RMT_SUPPORTED": false, "SOC_SDM_SUPPORTED": false, "SOC_GPSPI_SUPPORTED": false, "SOC_LEDC_SUPPORTED": false, "SOC_I2C_SUPPORTED": false, "SOC_SYSTIMER_SUPPORTED": false, "SOC_SUPPORT_COEXISTENCE": false, "SOC_AES_SUPPORTED": false, "SOC_MPI_SUPPORTED": false, "SOC_SHA_SUPPORTED": false, "SOC_HMAC_SUPPORTED": false, "SOC_DIG_SIGN_SUPPORTED": false, "SOC_FLASH_ENC_SUPPORTED": false, "SOC_SECURE_BOOT_SUPPORTED": false, "SOC_MEMPROT_SUPPORTED": false, "SOC_BOD_SUPPORTED": false, "SOC_CLK_TREE_SUPPORTED": false, "SOC_ASSIST_DEBUG_SUPPORTED": false, "SOC_WDT_SUPPORTED": false, "SOC_SPI_FLASH_SUPPORTED": false, "SOC_RNG_SUPPORTED": false, "SOC_LIGHT_SLEEP_SUPPORTED": false, "SOC_DEEP_SLEEP_SUPPORTED": false, "SOC_LP_PERIPH_SHARE_INTERRUPT": false, "SOC_PM_SUPPORTED": false, "SOC_XTAL_SUPPORT_40M": false, "SOC_AES_SUPPORT_DMA": false, "SOC_AES_GDMA": false, "SOC_AES_SUPPORT_AES_128": false, "SOC_AES_SUPPORT_AES_256": false, "SOC_ADC_DIG_CTRL_SUPPORTED": false, "SOC_ADC_ARBITER_SUPPORTED": false, "SOC_ADC_DIG_IIR_FILTER_SUPPORTED": false, "SOC_ADC_MONITOR_SUPPORTED": false, "SOC_ADC_DMA_SUPPORTED": false, "SOC_ADC_PERIPH_NUM": false, "SOC_ADC_MAX_CHANNEL_NUM": false, "SOC_ADC_ATTEN_NUM": false, "SOC_ADC_DIGI_CONTROLLER_NUM": false, "SOC_ADC_PATT_LEN_MAX": false, "SOC_ADC_DIGI_MIN_BITWIDTH": false, "SOC_ADC_DIGI_MAX_BITWIDTH": false, "SOC_ADC_DIGI_RESULT_BYTES": false, "SOC_ADC_DIGI_DATA_BYTES_PER_CONV": false, "SOC_ADC_DIGI_IIR_FILTER_NUM": false, "SOC_ADC_DIGI_MONITOR_NUM": false, "SOC_ADC_SAMPLE_FREQ_THRES_HIGH": false, "SOC_ADC_SAMPLE_FREQ_THRES_LOW": false, "SOC_ADC_RTC_MIN_BITWIDTH": false, "SOC_ADC_RTC_MAX_BITWIDTH": false, "SOC_ADC_CALIBRATION_V1_SUPPORTED": false, "SOC_ADC_SELF_HW_CALI_SUPPORTED": false, "SOC_ADC_SHARED_POWER": false, "SOC_APB_BACKUP_DMA": false, "SOC_BROWNOUT_RESET_SUPPORTED": false, "SOC_SHARED_IDCACHE_SUPPORTED": false, "SOC_CACHE_MEMORY_IBANK_SIZE": false, "SOC_CPU_CORES_NUM": false, "SOC_CPU_INTR_NUM": false, "SOC_CPU_HAS_FLEXIBLE_INTC": false, "SOC_CPU_HAS_CSR_PC": false, "SOC_CPU_BREAKPOINTS_NUM": false, "SOC_CPU_WATCHPOINTS_NUM": false, "SOC_CPU_WATCHPOINT_MAX_REGION_SIZE": false, "SOC_DS_SIGNATURE_MAX_BIT_LEN": false, "SOC_DS_KEY_PARAM_MD_IV_LENGTH": false, "SOC_DS_KEY_CHECK_MAX_WAIT_US": false, "SOC_AHB_GDMA_VERSION": false, "SOC_GDMA_NUM_GROUPS_MAX": false, "SOC_GDMA_PAIRS_PER_GROUP_MAX": false, "SOC_GPIO_PORT": false, "SOC_GPIO_PIN_COUNT": false, "SOC_GPIO_SUPPORT_PIN_GLITCH_FILTER": false, "SOC_GPIO_FILTER_CLK_SUPPORT_APB": false, "SOC_GPIO_SUPPORT_FORCE_HOLD": false, "SOC_GPIO_SUPPORT_DEEPSLEEP_WAKEUP": false, "SOC_GPIO_IN_RANGE_MAX": false, "SOC_GPIO_OUT_RANGE_MAX": false, "SOC_GPIO_DEEP_SLEEP_WAKE_VALID_GPIO_MASK": false, "SOC_GPIO_DEEP_SLEEP_WAKE_SUPPORTED_PIN_CNT": false, "SOC_GPIO_VALID_DIGITAL_IO_PAD_MASK": false, "SOC_GPIO_CLOCKOUT_BY_GPIO_MATRIX": false, "SOC_GPIO_CLOCKOUT_CHANNEL_NUM": false, "SOC_GPIO_SUPPORT_HOLD_IO_IN_DSLP": false, "SOC_DEDIC_GPIO_OUT_CHANNELS_NUM": false, "SOC_DEDIC_GPIO_IN_CHANNELS_NUM": false, "SOC_DEDIC_PERIPH_ALWAYS_ENABLE": false, "SOC_I2C_NUM": false, "SOC_HP_I2C_NUM": false, "SOC_I2C_FIFO_LEN": false, "SOC_I2C_CMD_REG_NUM": false, "SOC_I2C_SUPPORT_SLAVE": false, "SOC_I2C_SUPPORT_HW_CLR_BUS": false, "SOC_I2C_SUPPORT_XTAL": false, "SOC_I2C_SUPPORT_RTC": false, "SOC_I2C_SUPPORT_10BIT_ADDR": false, "SOC_I2C_SLAVE_SUPPORT_BROADCAST": false, "SOC_I2C_SLAVE_CAN_GET_STRETCH_CAUSE": false, "SOC_I2C_SLAVE_SUPPORT_I2CRAM_ACCESS": false, "SOC_I2S_NUM": false, "SOC_I2S_HW_VERSION_2": false, "SOC_I2S_SUPPORTS_XTAL": false, "SOC_I2S_SUPPORTS_PLL_F160M": false, "SOC_I2S_SUPPORTS_PCM": false, "SOC_I2S_SUPPORTS_PDM": false, "SOC_I2S_SUPPORTS_PDM_TX": false, "SOC_I2S_PDM_MAX_TX_LINES": false, "SOC_I2S_SUPPORTS_TDM": false, "SOC_LEDC_SUPPORT_APB_CLOCK": false, "SOC_LEDC_SUPPORT_XTAL_CLOCK": false, "SOC_LEDC_CHANNEL_NUM": false, "SOC_LEDC_TIMER_BIT_WIDTH": false, "SOC_LEDC_SUPPORT_FADE_STOP": false, "SOC_MMU_LINEAR_ADDRESS_REGION_NUM": false, "SOC_MMU_PERIPH_NUM": false, "SOC_MPU_CONFIGURABLE_REGIONS_SUPPORTED": false, "SOC_MPU_MIN_REGION_SIZE": false, "SOC_MPU_REGIONS_MAX_NUM": false, "SOC_MPU_REGION_RO_SUPPORTED": false, "SOC_MPU_REGION_WO_SUPPORTED": false, "SOC_RMT_GROUPS": false, "SOC_RMT_TX_CANDIDATES_PER_GROUP": false, "SOC_RMT_RX_CANDIDATES_PER_GROUP": false, "SOC_RMT_CHANNELS_PER_GROUP": false, "SOC_RMT_MEM_WORDS_PER_CHANNEL": false, "SOC_RMT_SUPPORT_RX_PINGPONG": false, "SOC_RMT_SUPPORT_RX_DEMODULATION": false, "SOC_RMT_SUPPORT_TX_ASYNC_STOP": false, "SOC_RMT_SUPPORT_TX_LOOP_COUNT": false, "SOC_RMT_SUPPORT_TX_SYNCHRO": false, "SOC_RMT_SUPPORT_TX_CARRIER_DATA_ONLY": false, "SOC_RMT_SUPPORT_XTAL": false, "SOC_RMT_SUPPORT_APB": false, "SOC_RMT_SUPPORT_RC_FAST": false, "SOC_RTC_CNTL_CPU_PD_DMA_BUS_WIDTH": false, "SOC_RTC_CNTL_CPU_PD_REG_FILE_NUM": false, "SOC_SLEEP_SYSTIMER_STALL_WORKAROUND": false, "SOC_SLEEP_TGWDT_STOP_WORKAROUND": false, "SOC_RTCIO_PIN_COUNT": false, "SOC_MPI_MEM_BLOCKS_NUM": false, "SOC_MPI_OPERATIONS_NUM": false, "SOC_RSA_MAX_BIT_LEN": false, "SOC_SHA_DMA_MAX_BUFFER_SIZE": false, "SOC_SHA_SUPPORT_DMA": false, "SOC_SHA_SUPPORT_RESUME": false, "SOC_SHA_GDMA": false, "SOC_SHA_SUPPORT_SHA1": false, "SOC_SHA_SUPPORT_SHA224": false, "SOC_SHA_SUPPORT_SHA256": false, "SOC_SDM_GROUPS": false, "SOC_SDM_CHANNELS_PER_GROUP": false, "SOC_SDM_CLK_SUPPORT_APB": false, "SOC_SPI_PERIPH_NUM": false, "SOC_SPI_MAX_CS_NUM": false, "SOC_SPI_MAXIMUM_BUFFER_SIZE": false, "SOC_SPI_SUPPORT_DDRCLK": false, "SOC_SPI_SLAVE_SUPPORT_SEG_TRANS": false, "SOC_SPI_SUPPORT_CD_SIG": false, "SOC_SPI_SUPPORT_CONTINUOUS_TRANS": false, "SOC_SPI_SUPPORT_SLAVE_HD_VER2": false, "SOC_SPI_SUPPORT_CLK_APB": false, "SOC_SPI_SUPPORT_CLK_XTAL": false, "SOC_SPI_PERIPH_SUPPORT_CONTROL_DUMMY_OUT": false, "SOC_SPI_SCT_SUPPORTED": false, "SOC_SPI_SCT_REG_NUM": false, "SOC_SPI_SCT_BUFFER_NUM_MAX": false, "SOC_SPI_SCT_CONF_BITLEN_MAX": false, "SOC_MEMSPI_IS_INDEPENDENT": false, "SOC_SPI_MAX_PRE_DIVIDER": false, "SOC_SPI_MEM_SUPPORT_AUTO_WAIT_IDLE": false, "SOC_SPI_MEM_SUPPORT_AUTO_SUSPEND": false, "SOC_SPI_MEM_SUPPORT_AUTO_RESUME": false, "SOC_SPI_MEM_SUPPORT_IDLE_INTR": false, "SOC_SPI_MEM_SUPPORT_SW_SUSPEND": false, "SOC_SPI_MEM_SUPPORT_CHECK_SUS": false, "SOC_SPI_MEM_SUPPORT_CONFIG_GPIO_BY_EFUSE": false, "SOC_SPI_MEM_SUPPORT_WRAP": false, "SOC_MEMSPI_SRC_FREQ_80M_SUPPORTED": false, "SOC_MEMSPI_SRC_FREQ_40M_SUPPORTED": false, "SOC_MEMSPI_SRC_FREQ_26M_SUPPORTED": false, "SOC_MEMSPI_SRC_FREQ_20M_SUPPORTED": false, "SOC_SYSTIMER_COUNTER_NUM": false, "SOC_SYSTIMER_ALARM_NUM": false, "SOC_SYSTIMER_BIT_WIDTH_LO": false, "SOC_SYSTIMER_BIT_WIDTH_HI": false, "SOC_SYSTIMER_FIXED_DIVIDER": false, "SOC_SYSTIMER_INT_LEVEL": false, "SOC_SYSTIMER_ALARM_MISS_COMPENSATE": false, "SOC_TIMER_GROUPS": false, "SOC_TIMER_GROUP_TIMERS_PER_GROUP": false, "SOC_TIMER_GROUP_COUNTER_BIT_WIDTH": false, "SOC_TIMER_GROUP_SUPPORT_XTAL": false, "SOC_TIMER_GROUP_SUPPORT_APB": false, "SOC_TIMER_GROUP_TOTAL_TIMERS": false, "SOC_LP_TIMER_BIT_WIDTH_LO": false, "SOC_LP_TIMER_BIT_WIDTH_HI": false, "SOC_MWDT_SUPPORT_XTAL": false, "SOC_TWAI_CONTROLLER_NUM": false, "SOC_TWAI_CLK_SUPPORT_APB": false, "SOC_TWAI_BRP_MIN": false, "SOC_TWAI_BRP_MAX": false, "SOC_TWAI_SUPPORTS_RX_STATUS": false, "SOC_EFUSE_DIS_DOWNLOAD_ICACHE": false, "SOC_EFUSE_DIS_PAD_JTAG": false, "SOC_EFUSE_DIS_USB_JTAG": false, "SOC_EFUSE_DIS_DIRECT_BOOT": false, "SOC_EFUSE_SOFT_DIS_JTAG": false, "SOC_EFUSE_DIS_ICACHE": false, "SOC_EFUSE_BLOCK9_KEY_PURPOSE_QUIRK": false, "SOC_SECURE_BOOT_V2_RSA": false, "SOC_EFUSE_SECURE_BOOT_KEY_DIGESTS": false, "SOC_EFUSE_REVOKE_BOOT_KEY_DIGESTS": false, "SOC_SUPPORT_SECURE_BOOT_REVOKE_KEY": false, "SOC_FLASH_ENCRYPTED_XTS_AES_BLOCK_MAX": false, "SOC_FLASH_ENCRYPTION_XTS_AES": false, "SOC_FLASH_ENCRYPTION_XTS_AES_128": false, "SOC_MEMPROT_CPU_PREFETCH_PAD_SIZE": false, "SOC_MEMPROT_MEM_ALIGN_SIZE": false, "SOC_UART_NUM": false, "SOC_UART_HP_NUM": false, "SOC_UART_FIFO_LEN": false, "SOC_UART_BITRATE_MAX": false, "SOC_UART_SUPPORT_APB_CLK": false, "SOC_UART_SUPPORT_RTC_CLK": false, "SOC_UART_SUPPORT_XTAL_CLK": false, "SOC_UART_SUPPORT_WAKEUP_INT": false, "SOC_UART_SUPPORT_FSM_TX_WAIT_SEND": false, "SOC_COEX_HW_PTI": false, "SOC_EXTERNAL_COEX_ADVANCE": false, "SOC_EXTERNAL_COEX_LEADER_TX_LINE": false, "SOC_PHY_DIG_REGS_MEM_SIZE": false, "SOC_MAC_BB_PD_MEM_SIZE": false, "SOC_WIFI_LIGHT_SLEEP_CLK_WIDTH": false, "SOC_PM_SUPPORT_WIFI_WAKEUP": false, "SOC_PM_SUPPORT_BT_WAKEUP": false, "SOC_PM_SUPPORT_CPU_PD": false, "SOC_PM_SUPPORT_WIFI_PD": false, "SOC_PM_SUPPORT_BT_PD": false, "SOC_PM_SUPPORT_RC_FAST_PD": false, "SOC_PM_SUPPORT_VDDSDIO_PD": false, "SOC_PM_SUPPORT_MAC_BB_PD": false, "SOC_PM_CPU_RETENTION_BY_RTCCNTL": false, "SOC_PM_MODEM_RETENTION_BY_BACKUPDMA": false, "SOC_CLK_RC_FAST_D256_SUPPORTED": false, "SOC_RTC_SLOW_CLK_SUPPORT_RC_FAST_D256": false, "SOC_CLK_RC_FAST_SUPPORT_CALIBRATION": false, "SOC_CLK_XTAL32K_SUPPORTED": false, "SOC_TEMPERATURE_SENSOR_SUPPORT_FAST_RC": false, "SOC_TEMPERATURE_SENSOR_SUPPORT_XTAL": false, "SOC_WIFI_HW_TSF": false, "SOC_WIFI_FTM_SUPPORT": false, "SOC_WIFI_GCMP_SUPPORT": false, "SOC_WIFI_WAPI_SUPPORT": false, "SOC_WIFI_CSI_SUPPORT": false, "SOC_WIFI_MESH_SUPPORT": false, "SOC_WIFI_SUPPORT_VARIABLE_BEACON_WINDOW": false, "SOC_WIFI_PHY_NEEDS_USB_WORKAROUND": false, "SOC_BLE_SUPPORTED": false, "SOC_BLE_MESH_SUPPORTED": false, "SOC_BLE_50_SUPPORTED": false, "SOC_BLE_DEVICE_PRIVACY_SUPPORTED": false, "SOC_BLUFI_SUPPORTED": false, "SOC_PHY_COMBO_MODULE": false, "IDF_CMAKE": false, "IDF_ENV_FPGA": false, "IDF_ENV_BRINGUP": false, "IDF_CI_BUILD": false, "IDF_DOC_BUILD": false, "IDF_TOOLCHAIN": false, "IDF_TOOLCHAIN_CLANG": false, "IDF_TARGET_ARCH_RISCV": false, "IDF_TARGET_ARCH_XTENSA": false, "IDF_TARGET_ARCH": false, "IDF_TARGET": false, "IDF_INIT_VERSION": false, "IDF_TARGET_ESP32": false, "IDF_TARGET_ESP32S2": false, "IDF_TARGET_ESP32S3": false, "IDF_TARGET_ESP32C3": false, "IDF_TARGET_ESP32C2": false, "IDF_TARGET_ESP32C6": false, "IDF_TARGET_ESP32C5": false, "esp32-c5-version": false, "IDF_TARGET_ESP32C5_BETA3_VERSION": false, "IDF_TARGET_ESP32C5_MP_VERSION": false, "IDF_TARGET_ESP32P4": false, "IDF_TARGET_ESP32H2": false, "IDF_TARGET_ESP32C61": false, "IDF_TARGET_LINUX": false, "IDF_FIRMWARE_CHIP_ID": false, "build-type-application-build-type": true, "APP_BUILD_TYPE_APP_2NDBOOT": true, "APP_BUILD_TYPE_RAM": true, "APP_BUILD_GENERATE_BINARIES": false, "APP_BUILD_BOOTLOADER": false, "APP_BUILD_TYPE_PURE_RAM_APP": false, "APP_BUILD_USE_FLASH_SECTIONS": false, "APP_REPRODUCIBLE_BUILD": true, "APP_NO_BLOBS": true, "APP_COMPATIBLE_PRE_V2_1_BOOTLOADERS": false, "APP_COMPATIBLE_PRE_V3_1_BOOTLOADERS": false, "APP_INIT_CLK": false, "BOOTLOADER_COMPILE_TIME_DATE": true, "BOOTLOADER_PROJECT_VER": true, "BOOTLOADER_OFFSET_IN_FLASH": false, "bootloader-config-bootloader-optimization-level": true, "BOOTLOADER_COMPILER_OPTIMIZATION_SIZE": true, "BOOTLOADER_COMPILER_OPTIMIZATION_DEBUG": true, "BOOTLOADER_COMPILER_OPTIMIZATION_PERF": true, "BOOTLOADER_COMPILER_OPTIMIZATION_NONE": true, "bootloader-config-bootloader-log-verbosity": true, "BOOTLOADER_LOG_LEVEL_NONE": true, "BOOTLOADER_LOG_LEVEL_ERROR": true, "BOOTLOADER_LOG_LEVEL_WARN": true, "BOOTLOADER_LOG_LEVEL_INFO": true, "BOOTLOADER_LOG_LEVEL_DEBUG": true, "BOOTLOADER_LOG_LEVEL_VERBOSE": true, "BOOTLOADER_LOG_LEVEL": false, "BOOTLOADER_SPI_CUSTOM_WP_PIN": false, "BOOTLOADER_SPI_WP_PIN": false, "BOOTLOADER_FLASH_DC_AWARE": true, "BOOTLOADER_FLASH_XMC_SUPPORT": true, "BOOTLOADER_FLASH_32BIT_ADDR": false, "BOOTLOADER_FLASH_NEEDS_32BIT_FEAT": false, "BOOTLOADER_FLASH_NEEDS_32BIT_ADDR_QUAD_FLASH": false, "BOOTLOADER_CACHE_32BIT_ADDR_QUAD_FLASH": false, "BOOTLOADER_CACHE_32BIT_ADDR_OCTAL_FLASH": false, "bootloader-config-vddsdio-ldo-voltage": false, "BOOTLOADER_VDDSDIO_BOOST_1_8V": false, "BOOTLOADER_VDDSDIO_BOOST_1_9V": false, "BOOTLOADER_FACTORY_RESET": true, "BOOTLOADER_NUM_PIN_FACTORY_RESET": false, "bootloader-config-gpio-triggers-factory-reset-factory-reset-gpio-level": false, "BOOTLOADER_FACTORY_RESET_PIN_LOW": false, "BOOTLOADER_FACTORY_RESET_PIN_HIGH": false, "BOOTLOADER_OTA_DATA_ERASE": false, "BOOTLOADER_DATA_FACTORY_RESET": false, "BOOTLOADER_APP_TEST": true, "BOOTLOADER_NUM_PIN_APP_TEST": false, "bootloader-config-gpio-triggers-boot-from-test-app-partition-app-test-gpio-level": false, "BOOTLOADER_APP_TEST_PIN_LOW": false, "BOOTLOADER_APP_TEST_PIN_HIGH": false, "BOOTLOADER_HOLD_TIME_GPIO": false, "BOOTLOADER_REGION_PROTECTION_ENABLE": true, "BOOTLOADER_WDT_ENABLE": true, "BOOTLOADER_WDT_DISABLE_IN_USER_CODE": true, "BOOTLOADER_WDT_TIME_MS": true, "BOOTLOADER_APP_ROLLBACK_ENABLE": true, "BOOTLOADER_APP_ANTI_ROLLBACK": false, "BOOTLOADER_APP_SECURE_VERSION": false, "BOOTLOADER_APP_SEC_VER_SIZE_EFUSE_FIELD": false, "BOOTLOADER_EFUSE_SECURE_VERSION_EMULATE": false, "BOOTLOADER_SKIP_VALIDATE_IN_DEEP_SLEEP": true, "BOOTLOADER_SKIP_VALIDATE_ON_POWER_ON": true, "BOOTLOADER_SKIP_VALIDATE_ALWAYS": true, "BOOTLOADER_RESERVE_RTC_SIZE": false, "BOOTLOADER_CUSTOM_RESERVE_RTC": true, "BOOTLOADER_CUSTOM_RESERVE_RTC_IN_CRC": false, "BOOTLOADER_CUSTOM_RESERVE_RTC_SIZE": false, "BOOTLOADER_RESERVE_RTC_MEM": false, "SECURE_SIGNED_ON_BOOT": false, "SECURE_SIGNED_ON_UPDATE": false, "SECURE_SIGNED_APPS": false, "SECURE_BOOT_V2_RSA_SUPPORTED": false, "SECURE_BOOT_V2_ECC_SUPPORTED": false, "SECURE_BOOT_V1_SUPPORTED": false, "SECURE_BOOT_V2_PREFERRED": false, "SECURE_BOOT_V2_ECDSA_ENABLED": false, "SECURE_BOOT_V2_RSA_ENABLED": false, "SECURE_BOOT_FLASH_ENC_KEYS_BURN_TOGETHER": false, "SECURE_SIGNED_APPS_NO_SECURE_BOOT": true, "security-features-app-signing-scheme": false, "SECURE_SIGNED_APPS_ECDSA_SCHEME": false, "SECURE_SIGNED_APPS_RSA_SCHEME": false, "SECURE_SIGNED_APPS_ECDSA_V2_SCHEME": false, "security-features-ecdsa-key-size": false, "SECURE_BOOT_ECDSA_KEY_LEN_192_BITS": false, "SECURE_BOOT_ECDSA_KEY_LEN_256_BITS": false, "SECURE_SIGNED_ON_BOOT_NO_SECURE_BOOT": false, "SECURE_SIGNED_ON_UPDATE_NO_SECURE_BOOT": false, "SECURE_BOOT": true, "security-features-enable-hardware-secure-boot-in-bootloader-read-docs-first--select-secure-boot-version": false, "SECURE_BOOT_V1_ENABLED": false, "SECURE_BOOT_V2_ENABLED": false, "security-features-secure-bootloader-mode": false, "SECURE_BOOTLOADER_ONE_TIME_FLASH": false, "SECURE_BOOTLOADER_REFLASHABLE": false, "SECURE_BOOT_BUILD_SIGNED_BINARIES": false, "SECURE_BOOT_SIGNING_KEY": false, "SECURE_BOOT_VERIFICATION_KEY": false, "SECURE_BOOT_ENABLE_AGGRESSIVE_KEY_REVOKE": false, "SECURE_BOOT_FLASH_BOOTLOADER_DEFAULT": false, "security-features-hardware-key-encoding": false, "SECURE_BOOTLOADER_KEY_ENCODING_256BIT": false, "SECURE_BOOTLOADER_KEY_ENCODING_192BIT": false, "SECURE_BOOT_INSECURE": false, "SECURE_FLASH_ENC_ENABLED": true, "security-features-enable-flash-encryption-on-boot-read-docs-first--size-of-generated-xts-aes-key": false, "SECURE_FLASH_ENCRYPTION_AES128_DERIVED": false, "SECURE_FLASH_ENCRYPTION_AES128": false, "SECURE_FLASH_ENCRYPTION_AES256": false, "security-features-enable-flash-encryption-on-boot-read-docs-first--enable-usage-mode": false, "SECURE_FLASH_ENCRYPTION_MODE_DEVELOPMENT": false, "SECURE_FLASH_ENCRYPTION_MODE_RELEASE": false, "SECURE_FLASH_HAS_WRITE_PROTECTION_CACHE": false, "SECURE_BOOT_ALLOW_ROM_BASIC": false, "SECURE_BOOT_ALLOW_JTAG": false, "SECURE_BOOT_ALLOW_SHORT_APP_PARTITION": false, "SECURE_BOOT_V2_ALLOW_EFUSE_RD_DIS": false, "SECURE_BOOT_ALLOW_UNUSED_DIGEST_SLOTS": false, "SECURE_FLASH_UART_BOOTLOADER_ALLOW_ENC": false, "SECURE_FLASH_UART_BOOTLOADER_ALLOW_DEC": false, "SECURE_FLASH_UART_BOOTLOADER_ALLOW_CACHE": false, "SECURE_FLASH_REQUIRE_ALREADY_ENABLED": false, "SECURE_FLASH_SKIP_WRITE_PROTECTION_CACHE": false, "SECURE_FLASH_ENCRYPT_ONLY_IMAGE_LEN_IN_APP_PART": false, "SECURE_FLASH_CHECK_ENC_EN_IN_APP": false, "SECURE_FLASH_PSEUDO_ROUND_FUNC": false, "security-features-permanently-enable-xts-aes-s-pseudo-rounds-function-strength-of-the-pseudo-rounds-function": false, "SECURE_FLASH_PSEUDO_ROUND_FUNC_STRENGTH_LOW": false, "SECURE_FLASH_PSEUDO_ROUND_FUNC_STRENGTH_MEDIUM": false, "SECURE_FLASH_PSEUDO_ROUND_FUNC_STRENGTH_HIGH": false, "SECURE_FLASH_PSEUDO_ROUND_FUNC_STRENGTH": false, "SECURE_ROM_DL_MODE_ENABLED": false, "security-features-uart-rom-download-mode": false, "SECURE_DISABLE_ROM_DL_MODE": false, "SECURE_ENABLE_SECURE_ROM_DL_MODE": false, "SECURE_INSECURE_ALLOW_DL_MODE": false, "APP_COMPILE_TIME_DATE": true, "APP_EXCLUDE_PROJECT_VER_VAR": true, "APP_EXCLUDE_PROJECT_NAME_VAR": true, "APP_PROJECT_VER_FROM_CONFIG": true, "APP_PROJECT_VER": false, "APP_RETRIEVE_LEN_ELF_SHA": true, "ESP_ROM_HAS_CRC_LE": false, "ESP_ROM_HAS_CRC_BE": false, "ESP_ROM_HAS_MZ_CRC32": false, "ESP_ROM_HAS_JPEG_DECODE": false, "ESP_ROM_UART_CLK_IS_XTAL": false, "ESP_ROM_USB_SERIAL_DEVICE_NUM": false, "ESP_ROM_HAS_RETARGETABLE_LOCKING": false, "ESP_ROM_HAS_ERASE_0_REGION_BUG": false, "ESP_ROM_HAS_ENCRYPTED_WRITES_USING_LEGACY_DRV": false, "ESP_ROM_GET_CLK_FREQ": false, "ESP_ROM_NEEDS_SWSETUP_WORKAROUND": false, "ESP_ROM_HAS_LAYOUT_TABLE": false, "ESP_ROM_HAS_SPI_FLASH": false, "ESP_ROM_HAS_ETS_PRINTF_BUG": false, "ESP_ROM_HAS_NEWLIB": false, "ESP_ROM_HAS_NEWLIB_NANO_FORMAT": false, "ESP_ROM_HAS_NEWLIB_32BIT_TIME": false, "ESP_ROM_NEEDS_SET_CACHE_MMU_SIZE": false, "ESP_ROM_RAM_APP_NEEDS_MMU_INIT": false, "ESP_ROM_HAS_SW_FLOAT": false, "ESP_ROM_USB_OTG_NUM": false, "ESP_ROM_HAS_VERSION": false, "ESP_ROM_SUPPORT_DEEP_SLEEP_WAKEUP_STUB": false, "boot-rom-behavior-permanently-change-boot-rom-output": true, "BOOT_ROM_LOG_ALWAYS_ON": true, "BOOT_ROM_LOG_ALWAYS_OFF": true, "BOOT_ROM_LOG_ON_GPIO_HIGH": true, "BOOT_ROM_LOG_ON_GPIO_LOW": true, "ESPTOOLPY_NO_STUB": true, "ESPTOOLPY_OCT_FLASH": false, "ESPTOOLPY_FLASH_MODE_AUTO_DETECT": false, "serial-flasher-config-flash-spi-mode": true, "ESPTOOLPY_FLASHMODE_QIO": true, "ESPTOOLPY_FLASHMODE_QOUT": true, "ESPTOOLPY_FLASHMODE_DIO": true, "ESPTOOLPY_FLASHMODE_DOUT": true, "ESPTOOLPY_FLASHMODE_OPI": false, "serial-flasher-config-flash-sampling-mode": true, "ESPTOOLPY_FLASH_SAMPLE_MODE_STR": true, "ESPTOOLPY_FLASH_SAMPLE_MODE_DTR": false, "ESPTOOLPY_FLASHMODE": false, "serial-flasher-config-flash-spi-speed": true, "ESPTOOLPY_FLASHFREQ_120M": false, "ESPTOOLPY_FLASHFREQ_80M": true, "ESPTOOLPY_FLASHFREQ_64M": false, "ESPTOOLPY_FLASHFREQ_60M": false, "ESPTOOLPY_FLASHFREQ_48M": false, "ESPTOOLPY_FLASHFREQ_40M": true, "ESPTOOLPY_FLASHFREQ_32M": false, "ESPTOOLPY_FLASHFREQ_30M": false, "ESPTOOLPY_FLASHFREQ_26M": true, "ESPTOOLPY_FLASHFREQ_24M": false, "ESPTOOLPY_FLASHFREQ_20M": true, "ESPTOOLPY_FLASHFREQ_16M": false, "ESPTOOLPY_FLASHFREQ_15M": false, "ESPTOOLPY_FLASHFREQ_VAL": false, "ESPTOOLPY_FLASHFREQ_80M_DEFAULT": false, "ESPTOOLPY_FLASHFREQ": false, "serial-flasher-config-flash-size": true, "ESPTOOLPY_FLASHSIZE_1MB": true, "ESPTOOLPY_FLASHSIZE_2MB": true, "ESPTOOLPY_FLASHSIZE_4MB": true, "ESPTOOLPY_FLASHSIZE_8MB": true, "ESPTOOLPY_FLASHSIZE_16MB": true, "ESPTOOLPY_FLASHSIZE_32MB": true, "ESPTOOLPY_FLASHSIZE_64MB": true, "ESPTOOLPY_FLASHSIZE_128MB": true, "ESPTOOLPY_FLASHSIZE": false, "ESPTOOLPY_HEADER_FLASHSIZE_UPDATE": true, "serial-flasher-config-before-flashing": true, "ESPTOOLPY_BEFORE_RESET": true, "ESPTOOLPY_BEFORE_NORESET": true, "ESPTOOLPY_BEFORE": false, "serial-flasher-config-after-flashing": true, "ESPTOOLPY_AFTER_RESET": true, "ESPTOOLPY_AFTER_NORESET": true, "ESPTOOLPY_AFTER": false, "ESPTOOLPY_MONITOR_BAUD": false, "partition-table-partition-table": true, "PARTITION_TABLE_SINGLE_APP": true, "PARTITION_TABLE_SINGLE_APP_LARGE": true, "PARTITION_TABLE_TWO_OTA": true, "PARTITION_TABLE_CUSTOM": true, "PARTITION_TABLE_SINGLE_APP_ENCRYPTED_NVS": false, "PARTITION_TABLE_SINGLE_APP_LARGE_ENC_NVS": false, "PARTITION_TABLE_TWO_OTA_ENCRYPTED_NVS": false, "PARTITION_TABLE_CUSTOM_FILENAME": true, "PARTITION_TABLE_FILENAME": false, "PARTITION_TABLE_OFFSET": true, "PARTITION_TABLE_MD5": true, "compiler-options-optimization-level": true, "COMPILER_OPTIMIZATION_DEBUG": true, "COMPILER_OPTIMIZATION_SIZE": true, "COMPILER_OPTIMIZATION_PERF": true, "COMPILER_OPTIMIZATION_NONE": true, "compiler-options-assertion-level": true, "COMPILER_OPTIMIZATION_ASSERTIONS_ENABLE": true, "COMPILER_OPTIMIZATION_ASSERTIONS_SILENT": true, "COMPILER_OPTIMIZATION_ASSERTIONS_DISABLE": true, "compiler-options-compiler-float-lib-source": true, "COMPILER_FLOAT_LIB_FROM_GCCLIB": true, "COMPILER_FLOAT_LIB_FROM_RVFPLIB": false, "COMPILER_OPTIMIZATION_ASSERTION_LEVEL": false, "COMPILER_OPTIMIZATION_CHECKS_SILENT": true, "COMPILER_HIDE_PATHS_MACROS": true, "COMPILER_CXX_EXCEPTIONS": true, "COMPILER_CXX_EXCEPTIONS_EMG_POOL_SIZE": false, "COMPILER_CXX_RTTI": true, "compiler-options-stack-smashing-protection-mode": true, "COMPILER_STACK_CHECK_MODE_NONE": true, "COMPILER_STACK_CHECK_MODE_NORM": true, "COMPILER_STACK_CHECK_MODE_STRONG": true, "COMPILER_STACK_CHECK_MODE_ALL": true, "COMPILER_STACK_CHECK": false, "COMPILER_NO_MERGE_CONSTANTS": false, "COMPILER_WARN_WRITE_STRINGS": true, "COMPILER_SAVE_RESTORE_LIBCALLS": true, "COMPILER_DISABLE_GCC12_WARNINGS": true, "COMPILER_DISABLE_GCC13_WARNINGS": true, "COMPILER_DUMP_RTL_FILES": true, "compiler-options-compiler-runtime-library": true, "COMPILER_RT_LIB_GCCLIB": true, "COMPILER_RT_LIB_CLANGRT": false, "COMPILER_RT_LIB_HOST": false, "COMPILER_RT_LIB_NAME": false, "compiler-options-orphan-sections-handling": true, "COMPILER_ORPHAN_SECTIONS_WARNING": true, "COMPILER_ORPHAN_SECTIONS_PLACE": true, "component-config-application-level-tracing-data-destination-1": true, "APPTRACE_DEST_JTAG": true, "APPTRACE_DEST_NONE": true, "APPTRACE_DEST_UART": false, "APPTRACE_DEST_UART_NOUSB": false, "component-config-application-level-tracing-data-destination-2": true, "APPTRACE_DEST_UART0": false, "APPTRACE_DEST_UART1": true, "APPTRACE_DEST_UART2": false, "APPTRACE_DEST_USB_CDC": true, "APPTRACE_DEST_UART_NONE": true, "APPTRACE_UART_TX_GPIO": false, "APPTRACE_UART_RX_GPIO": false, "APPTRACE_UART_BAUDRATE": false, "APPTRACE_UART_RX_BUFF_SIZE": false, "APPTRACE_UART_TX_BUFF_SIZE": false, "APPTRACE_UART_TX_MSG_SIZE": false, "APPTRACE_UART_TASK_PRIO": false, "APPTRACE_DEST_TRAX": false, "APPTRACE_MEMBUFS_APPTRACE_PROTO_ENABLE": false, "APPTRACE_ENABLE": false, "APPTRACE_LOCK_ENABLE": false, "APPTRACE_ONPANIC_HOST_FLUSH_TMO": false, "APPTRACE_POSTMORTEM_FLUSH_THRESH": false, "APPTRACE_BUF_SIZE": false, "APPTRACE_PENDING_DATA_SIZE_MAX": false, "APPTRACE_SV_ENABLE": false, "component-config-application-level-tracing-freertos-systemview-tracing-systemview-tracing-enable-systemview-destination": false, "APPTRACE_SV_DEST_JTAG": false, "APPTRACE_SV_DEST_UART": false, "component-config-application-level-tracing-freertos-systemview-tracing-cpu-to-trace": false, "APPTRACE_SV_DEST_CPU_0": false, "APPTRACE_SV_DEST_CPU_1": false, "component-config-application-level-tracing-freertos-systemview-tracing-timer-to-use-as-timestamp-source": false, "APPTRACE_SV_TS_SOURCE_CCOUNT": false, "APPTRACE_SV_TS_SOURCE_GPTIMER": false, "APPTRACE_SV_TS_SOURCE_ESP_TIMER": false, "APPTRACE_SV_MAX_TASKS": false, "APPTRACE_SV_BUF_WAIT_TMO": false, "APPTRACE_SV_EVT_OVERFLOW_ENABLE": false, "APPTRACE_SV_EVT_ISR_ENTER_ENABLE": false, "APPTRACE_SV_EVT_ISR_EXIT_ENABLE": false, "APPTRACE_SV_EVT_ISR_TO_SCHED_ENABLE": false, "APPTRACE_SV_EVT_TASK_START_EXEC_ENABLE": false, "APPTRACE_SV_EVT_TASK_STOP_EXEC_ENABLE": false, "APPTRACE_SV_EVT_TASK_START_READY_ENABLE": false, "APPTRACE_SV_EVT_TASK_STOP_READY_ENABLE": false, "APPTRACE_SV_EVT_TASK_CREATE_ENABLE": false, "APPTRACE_SV_EVT_TASK_TERMINATE_ENABLE": false, "APPTRACE_SV_EVT_IDLE_ENABLE": false, "APPTRACE_SV_EVT_TIMER_ENTER_ENABLE": false, "APPTRACE_SV_EVT_TIMER_EXIT_ENABLE": false, "APPTRACE_GCOV_ENABLE": false, "APPTRACE_GCOV_DUMP_TASK_STACK_SIZE": false, "BT_ENABLED": true, "component-config-bluetooth-bluetooth-host": true, "BT_BLUEDROID_ENABLED": true, "BT_NIMBLE_ENABLED": true, "BT_CONTROLLER_ONLY": true, "component-config-bluetooth-bluetooth-controller": true, "BT_CONTROLLER_ENABLED": true, "BT_CONTROLLER_DISABLED": true, "BT_BTC_TASK_STACK_SIZE": true, "component-config-bluetooth-bluedroid-options-the-cpu-core-which-bluedroid-run": false, "BT_BLUEDROID_PINNED_TO_CORE_0": false, "BT_BLUEDROID_PINNED_TO_CORE_1": false, "BT_BLUEDROID_PINNED_TO_CORE": false, "BT_BTU_TASK_STACK_SIZE": true, "BT_BLUEDROID_MEM_DEBUG": true, "BT_BLUEDROID_ESP_COEX_VSC": true, "BT_CLASSIC_ENABLED": false, "component-config-bluetooth-bluedroid-options-classic-bluetooth-configure-encryption-key-size": false, "BT_ENC_KEY_SIZE_CTRL_STD": false, "BT_ENC_KEY_SIZE_CTRL_VSC": false, "BT_ENC_KEY_SIZE_CTRL_NONE": false, "BT_CLASSIC_BQB_ENABLED": false, "BT_A2DP_ENABLE": false, "BT_SPP_ENABLED": false, "BT_L2CAP_ENABLED": false, "BT_SDP_PAD_LEN": false, "BT_SDP_ATTR_LEN": false, "BT_HFP_ENABLE": false, "BT_HFP_CLIENT_ENABLE": false, "BT_HFP_AG_ENABLE": false, "component-config-bluetooth-bluedroid-options-classic-bluetooth-hands-free-handset-profile-audio-sco-data-path": false, "BT_HFP_AUDIO_DATA_PATH_PCM": false, "BT_HFP_AUDIO_DATA_PATH_HCI": false, "BT_HFP_WBS_ENABLE": false, "BT_HID_ENABLED": false, "BT_HID_HOST_ENABLED": false, "BT_HID_DEVICE_ENABLED": false, "BT_HID_REMOVE_DEVICE_BONDING_ENABLED": false, "BT_BLE_ENABLED": true, "BT_GATTS_ENABLE": true, "BT_GATTS_PPCP_CHAR_GAP": true, "BT_BLE_BLUFI_ENABLE": true, "BT_GATT_MAX_SR_PROFILES": true, "BT_GATT_MAX_SR_ATTRIBUTES": true, "component-config-bluetooth-bluedroid-options-bluetooth-low-energy-include-gatt-server-module-gatts--gatts-service-change-mode": true, "BT_GATTS_SEND_SERVICE_CHANGE_MANUAL": true, "BT_GATTS_SEND_SERVICE_CHANGE_AUTO": true, "BT_GATTS_SEND_SERVICE_CHANGE_MODE": false, "BT_GATTS_ROBUST_CACHING_ENABLED": true, "BT_GATTS_DEVICE_NAME_WRITABLE": true, "BT_GATTS_APPEARANCE_WRITABLE": true, "BT_GATTC_ENABLE": true, "BT_GATTC_MAX_CACHE_CHAR": true, "BT_GATTC_NOTIF_REG_MAX": true, "BT_GATTC_CACHE_NVS_FLASH": true, "BT_GATTC_CONNECT_RETRY_COUNT": true, "BT_BLE_ESTAB_LINK_CONN_TOUT": true, "BT_BLE_SMP_ENABLE": true, "BT_SMP_SLAVE_CON_PARAMS_UPD_ENABLE": true, "BT_BLE_SMP_ID_RESET_ENABLE": true, "BT_BLE_SMP_BOND_NVS_FLASH": true, "BT_BLE_RPA_SUPPORTED": false, "BT_STACK_NO_LOG": true, "component-config-bluetooth-bluedroid-options-bt-debug-log-level-hci-layer": true, "BT_LOG_HCI_TRACE_LEVEL_NONE": true, "BT_LOG_HCI_TRACE_LEVEL_ERROR": true, "BT_LOG_HCI_TRACE_LEVEL_WARNING": true, "BT_LOG_HCI_TRACE_LEVEL_API": true, "BT_LOG_HCI_TRACE_LEVEL_EVENT": true, "BT_LOG_HCI_TRACE_LEVEL_DEBUG": true, "BT_LOG_HCI_TRACE_LEVEL_VERBOSE": true, "BT_LOG_HCI_TRACE_LEVEL": false, "component-config-bluetooth-bluedroid-options-bt-debug-log-level-btm-layer": true, "BT_LOG_BTM_TRACE_LEVEL_NONE": true, "BT_LOG_BTM_TRACE_LEVEL_ERROR": true, "BT_LOG_BTM_TRACE_LEVEL_WARNING": true, "BT_LOG_BTM_TRACE_LEVEL_API": true, "BT_LOG_BTM_TRACE_LEVEL_EVENT": true, "BT_LOG_BTM_TRACE_LEVEL_DEBUG": true, "BT_LOG_BTM_TRACE_LEVEL_VERBOSE": true, "BT_LOG_BTM_TRACE_LEVEL": false, "component-config-bluetooth-bluedroid-options-bt-debug-log-level-l2cap-layer": true, "BT_LOG_L2CAP_TRACE_LEVEL_NONE": true, "BT_LOG_L2CAP_TRACE_LEVEL_ERROR": true, "BT_LOG_L2CAP_TRACE_LEVEL_WARNING": true, "BT_LOG_L2CAP_TRACE_LEVEL_API": true, "BT_LOG_L2CAP_TRACE_LEVEL_EVENT": true, "BT_LOG_L2CAP_TRACE_LEVEL_DEBUG": true, "BT_LOG_L2CAP_TRACE_LEVEL_VERBOSE": true, "BT_LOG_L2CAP_TRACE_LEVEL": false, "component-config-bluetooth-bluedroid-options-bt-debug-log-level-rfcomm-layer": true, "BT_LOG_RFCOMM_TRACE_LEVEL_NONE": true, "BT_LOG_RFCOMM_TRACE_LEVEL_ERROR": true, "BT_LOG_RFCOMM_TRACE_LEVEL_WARNING": true, "BT_LOG_RFCOMM_TRACE_LEVEL_API": true, "BT_LOG_RFCOMM_TRACE_LEVEL_EVENT": true, "BT_LOG_RFCOMM_TRACE_LEVEL_DEBUG": true, "BT_LOG_RFCOMM_TRACE_LEVEL_VERBOSE": true, "BT_LOG_RFCOMM_TRACE_LEVEL": false, "component-config-bluetooth-bluedroid-options-bt-debug-log-level-sdp-layer": true, "BT_LOG_SDP_TRACE_LEVEL_NONE": true, "BT_LOG_SDP_TRACE_LEVEL_ERROR": true, "BT_LOG_SDP_TRACE_LEVEL_WARNING": true, "BT_LOG_SDP_TRACE_LEVEL_API": true, "BT_LOG_SDP_TRACE_LEVEL_EVENT": true, "BT_LOG_SDP_TRACE_LEVEL_DEBUG": true, "BT_LOG_SDP_TRACE_LEVEL_VERBOSE": true, "BT_LOG_SDP_TRACE_LEVEL": false, "component-config-bluetooth-bluedroid-options-bt-debug-log-level-gap-layer": true, "BT_LOG_GAP_TRACE_LEVEL_NONE": true, "BT_LOG_GAP_TRACE_LEVEL_ERROR": true, "BT_LOG_GAP_TRACE_LEVEL_WARNING": true, "BT_LOG_GAP_TRACE_LEVEL_API": true, "BT_LOG_GAP_TRACE_LEVEL_EVENT": true, "BT_LOG_GAP_TRACE_LEVEL_DEBUG": true, "BT_LOG_GAP_TRACE_LEVEL_VERBOSE": true, "BT_LOG_GAP_TRACE_LEVEL": false, "component-config-bluetooth-bluedroid-options-bt-debug-log-level-bnep-layer": true, "BT_LOG_BNEP_TRACE_LEVEL_NONE": true, "BT_LOG_BNEP_TRACE_LEVEL_ERROR": true, "BT_LOG_BNEP_TRACE_LEVEL_WARNING": true, "BT_LOG_BNEP_TRACE_LEVEL_API": true, "BT_LOG_BNEP_TRACE_LEVEL_EVENT": true, "BT_LOG_BNEP_TRACE_LEVEL_DEBUG": true, "BT_LOG_BNEP_TRACE_LEVEL_VERBOSE": true, "BT_LOG_BNEP_TRACE_LEVEL": false, "component-config-bluetooth-bluedroid-options-bt-debug-log-level-pan-layer": true, "BT_LOG_PAN_TRACE_LEVEL_NONE": true, "BT_LOG_PAN_TRACE_LEVEL_ERROR": true, "BT_LOG_PAN_TRACE_LEVEL_WARNING": true, "BT_LOG_PAN_TRACE_LEVEL_API": true, "BT_LOG_PAN_TRACE_LEVEL_EVENT": true, "BT_LOG_PAN_TRACE_LEVEL_DEBUG": true, "BT_LOG_PAN_TRACE_LEVEL_VERBOSE": true, "BT_LOG_PAN_TRACE_LEVEL": false, "component-config-bluetooth-bluedroid-options-bt-debug-log-level-a2d-layer": true, "BT_LOG_A2D_TRACE_LEVEL_NONE": true, "BT_LOG_A2D_TRACE_LEVEL_ERROR": true, "BT_LOG_A2D_TRACE_LEVEL_WARNING": true, "BT_LOG_A2D_TRACE_LEVEL_API": true, "BT_LOG_A2D_TRACE_LEVEL_EVENT": true, "BT_LOG_A2D_TRACE_LEVEL_DEBUG": true, "BT_LOG_A2D_TRACE_LEVEL_VERBOSE": true, "BT_LOG_A2D_TRACE_LEVEL": false, "component-config-bluetooth-bluedroid-options-bt-debug-log-level-avdt-layer": true, "BT_LOG_AVDT_TRACE_LEVEL_NONE": true, "BT_LOG_AVDT_TRACE_LEVEL_ERROR": true, "BT_LOG_AVDT_TRACE_LEVEL_WARNING": true, "BT_LOG_AVDT_TRACE_LEVEL_API": true, "BT_LOG_AVDT_TRACE_LEVEL_EVENT": true, "BT_LOG_AVDT_TRACE_LEVEL_DEBUG": true, "BT_LOG_AVDT_TRACE_LEVEL_VERBOSE": true, "BT_LOG_AVDT_TRACE_LEVEL": false, "component-config-bluetooth-bluedroid-options-bt-debug-log-level-avct-layer": true, "BT_LOG_AVCT_TRACE_LEVEL_NONE": true, "BT_LOG_AVCT_TRACE_LEVEL_ERROR": true, "BT_LOG_AVCT_TRACE_LEVEL_WARNING": true, "BT_LOG_AVCT_TRACE_LEVEL_API": true, "BT_LOG_AVCT_TRACE_LEVEL_EVENT": true, "BT_LOG_AVCT_TRACE_LEVEL_DEBUG": true, "BT_LOG_AVCT_TRACE_LEVEL_VERBOSE": true, "BT_LOG_AVCT_TRACE_LEVEL": false, "component-config-bluetooth-bluedroid-options-bt-debug-log-level-avrc-layer": true, "BT_LOG_AVRC_TRACE_LEVEL_NONE": true, "BT_LOG_AVRC_TRACE_LEVEL_ERROR": true, "BT_LOG_AVRC_TRACE_LEVEL_WARNING": true, "BT_LOG_AVRC_TRACE_LEVEL_API": true, "BT_LOG_AVRC_TRACE_LEVEL_EVENT": true, "BT_LOG_AVRC_TRACE_LEVEL_DEBUG": true, "BT_LOG_AVRC_TRACE_LEVEL_VERBOSE": true, "BT_LOG_AVRC_TRACE_LEVEL": false, "component-config-bluetooth-bluedroid-options-bt-debug-log-level-mca-layer": true, "BT_LOG_MCA_TRACE_LEVEL_NONE": true, "BT_LOG_MCA_TRACE_LEVEL_ERROR": true, "BT_LOG_MCA_TRACE_LEVEL_WARNING": true, "BT_LOG_MCA_TRACE_LEVEL_API": true, "BT_LOG_MCA_TRACE_LEVEL_EVENT": true, "BT_LOG_MCA_TRACE_LEVEL_DEBUG": true, "BT_LOG_MCA_TRACE_LEVEL_VERBOSE": true, "BT_LOG_MCA_TRACE_LEVEL": false, "component-config-bluetooth-bluedroid-options-bt-debug-log-level-hid-layer": true, "BT_LOG_HID_TRACE_LEVEL_NONE": true, "BT_LOG_HID_TRACE_LEVEL_ERROR": true, "BT_LOG_HID_TRACE_LEVEL_WARNING": true, "BT_LOG_HID_TRACE_LEVEL_API": true, "BT_LOG_HID_TRACE_LEVEL_EVENT": true, "BT_LOG_HID_TRACE_LEVEL_DEBUG": true, "BT_LOG_HID_TRACE_LEVEL_VERBOSE": true, "BT_LOG_HID_TRACE_LEVEL": false, "component-config-bluetooth-bluedroid-options-bt-debug-log-level-appl-layer": true, "BT_LOG_APPL_TRACE_LEVEL_NONE": true, "BT_LOG_APPL_TRACE_LEVEL_ERROR": true, "BT_LOG_APPL_TRACE_LEVEL_WARNING": true, "BT_LOG_APPL_TRACE_LEVEL_API": true, "BT_LOG_APPL_TRACE_LEVEL_EVENT": true, "BT_LOG_APPL_TRACE_LEVEL_DEBUG": true, "BT_LOG_APPL_TRACE_LEVEL_VERBOSE": true, "BT_LOG_APPL_TRACE_LEVEL": false, "component-config-bluetooth-bluedroid-options-bt-debug-log-level-gatt-layer": true, "BT_LOG_GATT_TRACE_LEVEL_NONE": true, "BT_LOG_GATT_TRACE_LEVEL_ERROR": true, "BT_LOG_GATT_TRACE_LEVEL_WARNING": true, "BT_LOG_GATT_TRACE_LEVEL_API": true, "BT_LOG_GATT_TRACE_LEVEL_EVENT": true, "BT_LOG_GATT_TRACE_LEVEL_DEBUG": true, "BT_LOG_GATT_TRACE_LEVEL_VERBOSE": true, "BT_LOG_GATT_TRACE_LEVEL": false, "component-config-bluetooth-bluedroid-options-bt-debug-log-level-smp-layer": true, "BT_LOG_SMP_TRACE_LEVEL_NONE": true, "BT_LOG_SMP_TRACE_LEVEL_ERROR": true, "BT_LOG_SMP_TRACE_LEVEL_WARNING": true, "BT_LOG_SMP_TRACE_LEVEL_API": true, "BT_LOG_SMP_TRACE_LEVEL_EVENT": true, "BT_LOG_SMP_TRACE_LEVEL_DEBUG": true, "BT_LOG_SMP_TRACE_LEVEL_VERBOSE": true, "BT_LOG_SMP_TRACE_LEVEL": false, "component-config-bluetooth-bluedroid-options-bt-debug-log-level-btif-layer": true, "BT_LOG_BTIF_TRACE_LEVEL_NONE": true, "BT_LOG_BTIF_TRACE_LEVEL_ERROR": true, "BT_LOG_BTIF_TRACE_LEVEL_WARNING": true, "BT_LOG_BTIF_TRACE_LEVEL_API": true, "BT_LOG_BTIF_TRACE_LEVEL_EVENT": true, "BT_LOG_BTIF_TRACE_LEVEL_DEBUG": true, "BT_LOG_BTIF_TRACE_LEVEL_VERBOSE": true, "BT_LOG_BTIF_TRACE_LEVEL": false, "component-config-bluetooth-bluedroid-options-bt-debug-log-level-btc-layer": true, "BT_LOG_BTC_TRACE_LEVEL_NONE": true, "BT_LOG_BTC_TRACE_LEVEL_ERROR": true, "BT_LOG_BTC_TRACE_LEVEL_WARNING": true, "BT_LOG_BTC_TRACE_LEVEL_API": true, "BT_LOG_BTC_TRACE_LEVEL_EVENT": true, "BT_LOG_BTC_TRACE_LEVEL_DEBUG": true, "BT_LOG_BTC_TRACE_LEVEL_VERBOSE": true, "BT_LOG_BTC_TRACE_LEVEL": false, "component-config-bluetooth-bluedroid-options-bt-debug-log-level-osi-layer": true, "BT_LOG_OSI_TRACE_LEVEL_NONE": true, "BT_LOG_OSI_TRACE_LEVEL_ERROR": true, "BT_LOG_OSI_TRACE_LEVEL_WARNING": true, "BT_LOG_OSI_TRACE_LEVEL_API": true, "BT_LOG_OSI_TRACE_LEVEL_EVENT": true, "BT_LOG_OSI_TRACE_LEVEL_DEBUG": true, "BT_LOG_OSI_TRACE_LEVEL_VERBOSE": true, "BT_LOG_OSI_TRACE_LEVEL": false, "component-config-bluetooth-bluedroid-options-bt-debug-log-level-blufi-layer": true, "BT_LOG_BLUFI_TRACE_LEVEL_NONE": true, "BT_LOG_BLUFI_TRACE_LEVEL_ERROR": true, "BT_LOG_BLUFI_TRACE_LEVEL_WARNING": true, "BT_LOG_BLUFI_TRACE_LEVEL_API": true, "BT_LOG_BLUFI_TRACE_LEVEL_EVENT": true, "BT_LOG_BLUFI_TRACE_LEVEL_DEBUG": true, "BT_LOG_BLUFI_TRACE_LEVEL_VERBOSE": true, "BT_LOG_BLUFI_TRACE_LEVEL": false, "BT_ACL_CONNECTIONS": true, "BT_MULTI_CONNECTION_ENBALE": true, "BT_ALLOCATION_FROM_SPIRAM_FIRST": true, "BT_BLE_DYNAMIC_ENV_MEMORY": true, "BT_SMP_ENABLE": false, "BT_SMP_MAX_BONDS": true, "BT_BLE_ACT_SCAN_REP_ADV_SCAN": true, "BT_MAX_DEVICE_NAME_LEN": true, "BT_BLE_RPA_TIMEOUT": true, "BT_BLE_50_FEATURES_SUPPORTED": true, "BT_BLE_50_EXTEND_ADV_EN": false, "BT_BLE_50_PERIODIC_ADV_EN": false, "BT_BLE_50_EXTEND_SCAN_EN": false, "BT_BLE_50_EXTEND_SYNC_EN": false, "BT_BLE_50_DTM_TEST_EN": false, "BT_BLE_FEAT_PERIODIC_ADV_SYNC_TRANSFER": false, "BT_BLE_FEAT_PERIODIC_ADV_ENH": false, "BT_BLE_FEAT_CREATE_SYNC_ENH": false, "BT_BLE_42_FEATURES_SUPPORTED": true, "BT_BLE_42_DTM_TEST_EN": true, "BT_BLE_42_ADV_EN": true, "BT_BLE_42_SCAN_EN": true, "BT_BLE_HIGH_DUTY_ADV_INTERVAL": true, "BT_ABORT_WHEN_ALLOCATION_FAILS": true, "component-config-bluetooth-nimble-options-memory-allocation-strategy": false, "BT_NIMBLE_MEM_ALLOC_MODE_INTERNAL": false, "BT_NIMBLE_MEM_ALLOC_MODE_EXTERNAL": false, "BT_NIMBLE_MEM_ALLOC_MODE_DEFAULT": false, "BT_NIMBLE_MEM_ALLOC_MODE_IRAM_8BIT": false, "component-config-bluetooth-nimble-options-nimble-host-log-verbosity": false, "BT_NIMBLE_LOG_LEVEL_NONE": false, "BT_NIMBLE_LOG_LEVEL_ERROR": false, "BT_NIMBLE_LOG_LEVEL_WARNING": false, "BT_NIMBLE_LOG_LEVEL_INFO": false, "BT_NIMBLE_LOG_LEVEL_DEBUG": false, "BT_NIMBLE_LOG_LEVEL": false, "BT_NIMBLE_MAX_CONNECTIONS": false, "BT_NIMBLE_MAX_BONDS": false, "BT_NIMBLE_MAX_CCCDS": false, "BT_NIMBLE_L2CAP_COC_MAX_NUM": false, "BT_NIMBLE_L2CAP_ENHANCED_COC": false, "component-config-bluetooth-nimble-options-the-cpu-core-on-which-nimble-host-will-run": false, "BT_NIMBLE_PINNED_TO_CORE_0": false, "BT_NIMBLE_PINNED_TO_CORE_1": false, "BT_NIMBLE_PINNED_TO_CORE": false, "BT_NIMBLE_HOST_TASK_STACK_SIZE": false, "BT_NIMBLE_ROLE_CENTRAL": false, "BT_NIMBLE_ROLE_PERIPHERAL": false, "BT_NIMBLE_ROLE_BROADCASTER": false, "BT_NIMBLE_ROLE_OBSERVER": false, "BT_NIMBLE_NVS_PERSIST": false, "BT_NIMBLE_SMP_ID_RESET": false, "BT_NIMBLE_SECURITY_ENABLE": false, "BT_NIMBLE_SM_LEGACY": false, "BT_NIMBLE_SM_SC": false, "BT_NIMBLE_SM_SC_DEBUG_KEYS": false, "BT_NIMBLE_LL_CFG_FEAT_LE_ENCRYPTION": false, "BT_NIMBLE_SM_LVL": false, "BT_NIMBLE_SM_SC_ONLY": false, "BT_NIMBLE_DEBUG": false, "BT_NIMBLE_DYNAMIC_SERVICE": false, "BT_NIMBLE_SVC_GAP_DEVICE_NAME": false, "BT_NIMBLE_GAP_DEVICE_NAME_MAX_LEN": false, "BT_NIMBLE_ATT_PREFERRED_MTU": false, "BT_NIMBLE_SVC_GAP_APPEARANCE": false, "BT_NIMBLE_MSYS_1_BLOCK_COUNT": false, "BT_NIMBLE_MSYS_1_BLOCK_SIZE": false, "BT_NIMBLE_MSYS_2_BLOCK_COUNT": false, "BT_NIMBLE_MSYS_2_BLOCK_SIZE": false, "BT_NIMBLE_MSYS_BUF_FROM_HEAP": false, "BT_NIMBLE_TRANSPORT_ACL_FROM_LL_COUNT": false, "BT_NIMBLE_TRANSPORT_ACL_SIZE": false, "BT_NIMBLE_TRANSPORT_EVT_SIZE": false, "BT_NIMBLE_TRANSPORT_EVT_COUNT": false, "BT_NIMBLE_TRANSPORT_EVT_DISCARD_COUNT": false, "BT_NIMBLE_L2CAP_COC_SDU_BUFF_COUNT": false, "BT_NIMBLE_GATT_MAX_PROCS": false, "BT_NIMBLE_HS_FLOW_CTRL": false, "BT_NIMBLE_HS_FLOW_CTRL_ITVL": false, "BT_NIMBLE_HS_FLOW_CTRL_THRESH": false, "BT_NIMBLE_HS_FLOW_CTRL_TX_ON_DISCONNECT": false, "BT_NIMBLE_RPA_TIMEOUT": false, "BT_NIMBLE_MESH": false, "BT_NIMBLE_MESH_PROXY": false, "BT_NIMBLE_MESH_PROV": false, "BT_NIMBLE_MESH_PB_ADV": false, "BT_NIMBLE_MESH_PB_GATT": false, "BT_NIMBLE_MESH_GATT_PROXY": false, "BT_NIMBLE_MESH_RELAY": false, "BT_NIMBLE_MESH_LOW_POWER": false, "BT_NIMBLE_MESH_FRIEND": false, "BT_NIMBLE_MESH_DEVICE_NAME": false, "BT_NIMBLE_MESH_NODE_COUNT": false, "BT_NIMBLE_MESH_PROVISIONER": false, "BT_NIMBLE_CRYPTO_STACK_MBEDTLS": false, "BT_NIMBLE_HS_STOP_TIMEOUT_MS": false, "BT_NIMBLE_HOST_BASED_PRIVACY": false, "BT_NIMBLE_ENABLE_CONN_REATTEMPT": false, "BT_NIMBLE_MAX_CONN_REATTEMPT": false, "BT_NIMBLE_50_FEATURE_SUPPORT": false, "BT_NIMBLE_LL_CFG_FEAT_LE_2M_PHY": false, "BT_NIMBLE_LL_CFG_FEAT_LE_CODED_PHY": false, "BT_NIMBLE_EXT_ADV": false, "BT_NIMBLE_MAX_EXT_ADV_INSTANCES": false, "BT_NIMBLE_EXT_ADV_MAX_SIZE": false, "BT_NIMBLE_ENABLE_PERIODIC_ADV": false, "BT_NIMBLE_PERIODIC_ADV_ENH": false, "BT_NIMBLE_PERIODIC_ADV_SYNC_TRANSFER": false, "BT_NIMBLE_PERIODIC_ADV_WITH_RESPONSES": false, "BT_NIMBLE_EXT_SCAN": false, "BT_NIMBLE_ENABLE_PERIODIC_SYNC": false, "BT_NIMBLE_MAX_PERIODIC_SYNCS": false, "BT_NIMBLE_MAX_PERIODIC_ADVERTISER_LIST": false, "BT_NIMBLE_BLE_POWER_CONTROL": false, "BT_NIMBLE_AOA_AOD": false, "BT_NIMBLE_GATT_CACHING": false, "BT_NIMBLE_GATT_CACHING_MAX_CONNS": false, "BT_NIMBLE_GATT_CACHING_MAX_SVCS": false, "BT_NIMBLE_GATT_CACHING_MAX_CHRS": false, "BT_NIMBLE_GATT_CACHING_MAX_DSCS": false, "BT_NIMBLE_GATT_CACHING_DISABLE_AUTO": false, "BT_NIMBLE_WHITELIST_SIZE": false, "BT_NIMBLE_TEST_THROUGHPUT_TEST": false, "BT_NIMBLE_BLUFI_ENABLE": false, "BT_NIMBLE_USE_ESP_TIMER": false, "BT_NIMBLE_LEGACY_VHCI_ENABLE": false, "BT_NIMBLE_BLE_GATT_BLOB_TRANSFER": false, "BT_NIMBLE_SVC_GAP_APPEAR_WRITE": false, "BT_NIMBLE_SVC_GAP_APPEAR_WRITE_ENC": false, "BT_NIMBLE_SVC_GAP_APPEAR_WRITE_AUTHEN": false, "BT_NIMBLE_SVC_GAP_APPEAR_WRITE_AUTHOR": false, "BT_NIMBLE_SVC_GAP_APPEAR_WRITE_PERM": false, "BT_NIMBLE_SVC_GAP_APPEAR_WRITE_PERM_ENC": false, "BT_NIMBLE_SVC_GAP_APPEAR_WRITE_PERM_ATHN": false, "BT_NIMBLE_SVC_GAP_APPEAR_WRITE_PERM_ATHR": false, "component-config-bluetooth-nimble-options-gap-service-gap-characteristic-central-address-resolution": false, "BT_NIMBLE_SVC_GAP_CAR_CHAR_NOT_SUPP": false, "BT_NIMBLE_SVC_GAP_CAR_NOT_SUPP": false, "BT_NIMBLE_SVC_GAP_CAR_SUPP": false, "BT_NIMBLE_SVC_GAP_CENT_ADDR_RESOLUTION": false, "BT_NIMBLE_SVC_GAP_NAME_WRITE": false, "BT_NIMBLE_SVC_GAP_NAME_WRITE_ENC": false, "BT_NIMBLE_SVC_GAP_NAME_WRITE_AUTHEN": false, "BT_NIMBLE_SVC_GAP_NAME_WRITE_AUTHOR": false, "BT_NIMBLE_SVC_GAP_NAME_WRITE_PERM": false, "BT_NIMBLE_SVC_GAP_NAME_WRITE_PERM_ENC": false, "BT_NIMBLE_SVC_GAP_NAME_WRITE_PERM_AUTHEN": false, "BT_NIMBLE_SVC_GAP_NAME_WRITE_PERM_AUTHOR": false, "BT_NIMBLE_SVC_GAP_PPCP_MAX_CONN_INTERVAL": false, "BT_NIMBLE_SVC_GAP_PPCP_MIN_CONN_INTERVAL": false, "BT_NIMBLE_SVC_GAP_PPCP_SLAVE_LATENCY": false, "BT_NIMBLE_SVC_GAP_PPCP_SUPERVISION_TMO": false, "BT_NIMBLE_SVC_GAP_GATT_SECURITY_LEVEL": false, "BT_NIMBLE_HID_SERVICE": false, "BT_NIMBLE_SVC_HID_MAX_INSTANCES": false, "BT_NIMBLE_SVC_HID_MAX_RPTS": false, "BT_NIMBLE_SVC_BAS_BATTERY_LEVEL_NOTIFY": false, "BT_NIMBLE_SVC_DIS_MANUFACTURER_NAME": false, "BT_NIMBLE_SVC_DIS_SERIAL_NUMBER": false, "BT_NIMBLE_SVC_DIS_HARDWARE_REVISION": false, "BT_NIMBLE_SVC_DIS_FIRMWARE_REVISION": false, "BT_NIMBLE_SVC_DIS_SOFTWARE_REVISION": false, "BT_NIMBLE_SVC_DIS_SYSTEM_ID": false, "BT_NIMBLE_SVC_DIS_PNP_ID": false, "BT_NIMBLE_SVC_DIS_INCLUDED": false, "BT_NIMBLE_VS_SUPPORT": false, "BT_NIMBLE_OPTIMIZE_MULTI_CONN": false, "BT_NIMBLE_ENC_ADV_DATA": false, "BT_NIMBLE_MAX_EADS": false, "BT_NIMBLE_HIGH_DUTY_ADV_ITVL": false, "BT_NIMBLE_HOST_ALLOW_CONNECT_WITH_SCAN": false, "BT_NIMBLE_HOST_QUEUE_CONG_CHECK": false, "BT_NIMBLE_GATTC_PROC_PREEMPTION_PROTECT": false, "BT_NIMBLE_TRANSPORT_UART": false, "BT_NIMBLE_TRANSPORT_UART_PORT": false, "component-config-bluetooth-nimble-options-host-controller-transport-enable-uart-transport-uart-hci-baud-rate": false, "UART_BAUDRATE_115200": false, "UART_BAUDRATE_230400": false, "UART_BAUDRATE_460800": false, "UART_BAUDRATE_921600": false, "BT_NIMBLE_HCI_UART_BAUDRATE": false, "component-config-bluetooth-nimble-options-host-controller-transport-enable-uart-transport-uart-parity": false, "UART_PARITY_NONE": false, "UART_PARITY_ODD": false, "UART_PARITY_EVEN": false, "BT_NIMBLE_TRANSPORT_UART_PARITY_NONE": false, "BT_NIMBLE_TRANSPORT_UART_PARITY_ODD": false, "BT_NIMBLE_TRANSPORT_UART_PARITY_EVEN": false, "BT_NIMBLE_UART_RX_PIN": false, "BT_NIMBLE_UART_TX_PIN": false, "component-config-bluetooth-nimble-options-host-controller-transport-uart-flow-control": false, "UART_HW_FLOWCTRL_DISABLE": false, "UART_HW_FLOWCTRL_CTS_RTS": false, "BT_NIMBLE_HCI_UART_FLOW_CTRL": false, "BT_NIMBLE_HCI_UART_RTS_PIN": false, "BT_NIMBLE_HCI_UART_CTS_PIN": false, "BT_NIMBLE_EATT_CHAN_NUM": false, "BT_NIMBLE_SUBRATE": false, "BT_CTRL_MODE_EFF": false, "BT_CTRL_BLE_MAX_ACT": true, "BT_CTRL_BLE_MAX_ACT_EFF": false, "BT_CTRL_BLE_STATIC_ACL_TX_BUF_NB": true, "component-config-bluetooth-controller-options-the-cpu-core-which-bluetooth-controller-run": false, "BT_CTRL_PINNED_TO_CORE_0": false, "BT_CTRL_PINNED_TO_CORE_1": false, "BT_CTRL_PINNED_TO_CORE": false, "component-config-bluetooth-controller-options-hci-mode": true, "BT_CTRL_HCI_MODE_VHCI": true, "BT_CTRL_HCI_MODE_UART_H4": true, "BT_CTRL_HCI_TL": false, "BT_CTRL_ADV_DUP_FILT_MAX": true, "component-config-bluetooth-controller-options-ble-cca-mode": true, "BT_BLE_CCA_MODE_NONE": true, "BT_BLE_CCA_MODE_HW": true, "BT_BLE_CCA_MODE_SW": true, "BT_BLE_CCA_MODE": false, "BT_CTRL_HW_CCA_VAL": true, "BT_CTRL_HW_CCA_EFF": false, "component-config-bluetooth-controller-options-connection-event-length-determination-method": true, "BT_CTRL_CE_LENGTH_TYPE_ORIG": true, "BT_CTRL_CE_LENGTH_TYPE_CE": true, "BT_CTRL_CE_LENGTH_TYPE_SD": true, "BT_CTRL_CE_LENGTH_TYPE_EFF": false, "component-config-bluetooth-controller-options-default-tx-anntena-used": true, "BT_CTRL_TX_ANTENNA_INDEX_0": true, "BT_CTRL_TX_ANTENNA_INDEX_1": true, "BT_CTRL_TX_ANTENNA_INDEX_EFF": false, "component-config-bluetooth-controller-options-default-rx-anntena-used": true, "BT_CTRL_RX_ANTENNA_INDEX_0": true, "BT_CTRL_RX_ANTENNA_INDEX_1": true, "BT_CTRL_RX_ANTENNA_INDEX_EFF": false, "component-config-bluetooth-controller-options-ble-default-tx-power-level": true, "BT_CTRL_DFT_TX_POWER_LEVEL_N24": true, "BT_CTRL_DFT_TX_POWER_LEVEL_N21": true, "BT_CTRL_DFT_TX_POWER_LEVEL_N18": true, "BT_CTRL_DFT_TX_POWER_LEVEL_N15": true, "BT_CTRL_DFT_TX_POWER_LEVEL_N12": true, "BT_CTRL_DFT_TX_POWER_LEVEL_N9": true, "BT_CTRL_DFT_TX_POWER_LEVEL_N6": true, "BT_CTRL_DFT_TX_POWER_LEVEL_N3": true, "BT_CTRL_DFT_TX_POWER_LEVEL_N0": true, "BT_CTRL_DFT_TX_POWER_LEVEL_P3": true, "BT_CTRL_DFT_TX_POWER_LEVEL_P6": true, "BT_CTRL_DFT_TX_POWER_LEVEL_P9": true, "BT_CTRL_DFT_TX_POWER_LEVEL_P12": true, "BT_CTRL_DFT_TX_POWER_LEVEL_P15": true, "BT_CTRL_DFT_TX_POWER_LEVEL_P18": true, "BT_CTRL_DFT_TX_POWER_LEVEL_P20": true, "BT_CTRL_DFT_TX_POWER_LEVEL_EFF": false, "BT_CTRL_BLE_ADV_REPORT_FLOW_CTRL_SUPP": true, "BT_CTRL_BLE_ADV_REPORT_FLOW_CTRL_NUM": true, "BT_CTRL_BLE_ADV_REPORT_DISCARD_THRSHOLD": true, "BT_CTRL_BLE_SCAN_DUPL": true, "component-config-bluetooth-controller-options-ble-scan-duplicate-options-scan-duplicate-type": true, "BT_CTRL_SCAN_DUPL_TYPE_DEVICE": true, "BT_CTRL_SCAN_DUPL_TYPE_DATA": true, "BT_CTRL_SCAN_DUPL_TYPE_DATA_DEVICE": true, "BT_CTRL_SCAN_DUPL_TYPE": false, "BT_CTRL_SCAN_DUPL_CACHE_SIZE": true, "BT_CTRL_DUPL_SCAN_CACHE_REFRESH_PERIOD": true, "BT_CTRL_BLE_MESH_SCAN_DUPL_EN": true, "BT_CTRL_MESH_DUPL_SCAN_CACHE_SIZE": false, "component-config-bluetooth-controller-options-coexistence-limit-on-max-tx-rx-time-for-coded-phy-connection": true, "BT_CTRL_COEX_PHY_CODED_TX_RX_TLIM_EN": true, "BT_CTRL_COEX_PHY_CODED_TX_RX_TLIM_DIS": true, "BT_CTRL_COEX_PHY_CODED_TX_RX_TLIM_EFF": false, "BT_CTRL_MODEM_SLEEP": true, "BT_CTRL_MODEM_SLEEP_MODE_1": false, "component-config-bluetooth-controller-options-modem-sleep-options-bluetooth-modem-sleep-bluetooth-modem-sleep-mode-1-bluetooth-low-power-clock": false, "BT_CTRL_LPCLK_SEL_MAIN_XTAL": false, "BT_CTRL_LPCLK_SEL_EXT_32K_XTAL": false, "BT_CTRL_LPCLK_SEL_RTC_SLOW": false, "BT_CTRL_MAIN_XTAL_PU_DURING_LIGHT_SLEEP": false, "BT_CTRL_SLEEP_MODE_EFF": false, "BT_CTRL_SLEEP_CLOCK_EFF": false, "BT_CTRL_HCI_TL_EFF": false, "BT_CTRL_AGC_RECORRECT_EN": true, "BT_CTRL_CODED_AGC_RECORRECT_EN": false, "BT_CTRL_SCAN_BACKOFF_UPPERLIMITMAX": true, "BT_BLE_ADV_DATA_LENGTH_ZERO_AUX": true, "BT_CTRL_CHAN_ASS_EN": true, "BT_CTRL_LE_PING_EN": true, "BT_CTRL_BLE_LLCP_CONN_UPDATE": true, "BT_CTRL_BLE_LLCP_CHAN_MAP_UPDATE": true, "BT_CTRL_BLE_LLCP_PHY_UPDATE": true, "BT_CTRL_RUN_IN_FLASH_ONLY": true, "BT_CTRL_DTM_ENABLE": false, "BT_CTRL_BLE_MASTER": false, "BT_CTRL_BLE_TEST": false, "BT_CTRL_BLE_SCAN": false, "BT_CTRL_BLE_SECURITY_ENABLE": false, "BT_CTRL_CHECK_CONNECT_IND_ACCESS_ADDRESS": true, "BT_CTRL_LE_LOG_EN": false, "BT_CTRL_LE_HCI_LOG_EN": false, "BT_CTRL_LE_LOG_DUMP_ONLY": false, "BT_CTRL_LE_LOG_STORAGE_EN": false, "BT_CTRL_LE_LOG_PARTITION_SIZE": false, "BT_CTRL_LE_LOG_SPI_OUT_EN": false, "BT_CTRL_LE_LOG_MODE_EN": false, "BT_CTRL_LE_LOG_LEVEL": false, "BT_CTRL_LE_LOG_BUF1_SIZE": false, "BT_CTRL_LE_LOG_HCI_BUF_SIZE": false, "BT_CTRL_LE_LOG_BUF2_SIZE": false, "BT_RELEASE_IRAM": false, "BT_ALARM_MAX_NUM": true, "BT_BLE_LOG_SPI_OUT_ENABLED": true, "BT_BLE_LOG_SPI_OUT_HCI_ENABLED": false, "BT_BLE_LOG_SPI_OUT_HOST_ENABLED": false, "BT_BLE_LOG_SPI_OUT_QUEUE_SIZE": false, "BT_BLE_LOG_SPI_OUT_TRANS_BUF_SIZE": false, "BT_BLE_LOG_SPI_OUT_MOSI_IO_NUM": false, "BT_BLE_LOG_SPI_OUT_SCLK_IO_NUM": false, "BT_BLE_LOG_SPI_OUT_CS_IO_NUM": false, "BT_BLE_LOG_SPI_OUT_TS_SYNC_ENABLED": false, "BT_BLE_LOG_SPI_OUT_SYNC_IO_NUM": false, "BT_HCI_LOG_DEBUG_EN": true, "BT_HCI_LOG_DATA_BUFFER_SIZE": false, "BT_HCI_LOG_ADV_BUFFER_SIZE": false, "BLE_MESH": true, "BLE_MESH_HCI_5_0": false, "BLE_MESH_V11_SUPPORT": false, "BLE_MESH_RANDOM_ADV_INTERVAL": false, "BLE_MESH_USE_DUPLICATE_SCAN": false, "BLE_MESH_ACTIVE_SCAN": false, "component-config-esp-ble-mesh-support-memory-allocation-strategy": false, "BLE_MESH_MEM_ALLOC_MODE_INTERNAL": false, "BLE_MESH_MEM_ALLOC_MODE_EXTERNAL": false, "BLE_MESH_MEM_ALLOC_MODE_DEFAULT": false, "BLE_MESH_MEM_ALLOC_MODE_IRAM_8BIT": false, "BLE_MESH_FREERTOS_STATIC_ALLOC": false, "component-config-esp-ble-mesh-support-enable-freertos-static-allocation-memory-allocation-for-freertos-objects": false, "BLE_MESH_FREERTOS_STATIC_ALLOC_EXTERNAL": false, "BLE_MESH_FREERTOS_STATIC_ALLOC_IRAM_8BIT": false, "BLE_MESH_DEINIT": false, "BLE_MESH_SUPPORT_BLE_ADV": false, "BLE_MESH_BLE_ADV_BUF_COUNT": false, "BLE_MESH_SUPPORT_BLE_SCAN": false, "BLE_MESH_FAST_PROV": false, "BLE_MESH_NODE": false, "BLE_MESH_PROVISIONER": false, "BLE_MESH_WAIT_FOR_PROV_MAX_DEV_NUM": false, "BLE_MESH_MAX_PROV_NODES": false, "BLE_MESH_PBA_SAME_TIME": false, "BLE_MESH_PBG_SAME_TIME": false, "BLE_MESH_PROVISIONER_SUBNET_COUNT": false, "BLE_MESH_PROVISIONER_APP_KEY_COUNT": false, "BLE_MESH_PROVISIONER_RECV_HB": false, "BLE_MESH_PROVISIONER_RECV_HB_FILTER_SIZE": false, "BLE_MESH_PROV": false, "BLE_MESH_PROV_EPA": false, "BLE_MESH_CERT_BASED_PROV": false, "BLE_MESH_RECORD_FRAG_MAX_SIZE": false, "BLE_MESH_PB_ADV": false, "BLE_MESH_UNPROVISIONED_BEACON_INTERVAL": false, "BLE_MESH_PB_GATT": false, "BLE_MESH_PROXY": false, "BLE_MESH_GATT_PROXY_SERVER": false, "BLE_MESH_NODE_ID_TIMEOUT": false, "BLE_MESH_PROXY_FILTER_SIZE": false, "BLE_MESH_PROXY_PRIVACY": false, "BLE_MESH_PROXY_SOLIC_PDU_RX": false, "BLE_MESH_PROXY_SOLIC_RX_CRPL": false, "BLE_MESH_GATT_PROXY_CLIENT": false, "BLE_MESH_PROXY_SOLIC_PDU_TX": false, "BLE_MESH_PROXY_SOLIC_TX_SRC_COUNT": false, "BLE_MESH_NET_BUF_POOL_USAGE": false, "BLE_MESH_SETTINGS": false, "BLE_MESH_STORE_TIMEOUT": false, "BLE_MESH_SEQ_STORE_RATE": false, "BLE_MESH_RPL_STORE_TIMEOUT": false, "BLE_MESH_SETTINGS_BACKWARD_COMPATIBILITY": false, "BLE_MESH_SPECIFIC_PARTITION": false, "BLE_MESH_PARTITION_NAME": false, "BLE_MESH_USE_MULTIPLE_NAMESPACE": false, "BLE_MESH_MAX_NVS_NAMESPACE": false, "BLE_MESH_SUBNET_COUNT": false, "BLE_MESH_APP_KEY_COUNT": false, "BLE_MESH_MODEL_KEY_COUNT": false, "BLE_MESH_MODEL_GROUP_COUNT": false, "BLE_MESH_LABEL_COUNT": false, "BLE_MESH_CRPL": false, "BLE_MESH_NOT_RELAY_REPLAY_MSG": false, "BLE_MESH_MSG_CACHE_SIZE": false, "BLE_MESH_ADV_BUF_COUNT": false, "BLE_MESH_IVU_DIVIDER": false, "BLE_MESH_IVU_RECOVERY_IVI": false, "BLE_MESH_SAR_ENHANCEMENT": false, "BLE_MESH_TX_SEG_MSG_COUNT": false, "BLE_MESH_RX_SEG_MSG_COUNT": false, "BLE_MESH_RX_SDU_MAX": false, "BLE_MESH_TX_SEG_MAX": false, "BLE_MESH_RELAY": false, "BLE_MESH_RELAY_ADV_BUF": false, "BLE_MESH_RELAY_ADV_BUF_COUNT": false, "BLE_MESH_LOW_POWER": false, "BLE_MESH_LPN_ESTABLISHMENT": false, "BLE_MESH_LPN_AUTO": false, "BLE_MESH_LPN_AUTO_TIMEOUT": false, "BLE_MESH_LPN_RETRY_TIMEOUT": false, "BLE_MESH_LPN_RSSI_FACTOR": false, "BLE_MESH_LPN_RECV_WIN_FACTOR": false, "BLE_MESH_LPN_MIN_QUEUE_SIZE": false, "BLE_MESH_LPN_RECV_DELAY": false, "BLE_MESH_LPN_POLL_TIMEOUT": false, "BLE_MESH_LPN_INIT_POLL_TIMEOUT": false, "BLE_MESH_LPN_SCAN_LATENCY": false, "BLE_MESH_LPN_GROUPS": false, "BLE_MESH_LPN_SUB_ALL_NODES_ADDR": false, "BLE_MESH_FRIEND": false, "BLE_MESH_FRIEND_RECV_WIN": false, "BLE_MESH_FRIEND_QUEUE_SIZE": false, "BLE_MESH_FRIEND_SUB_LIST_SIZE": false, "BLE_MESH_FRIEND_LPN_COUNT": false, "BLE_MESH_FRIEND_SEG_RX": false, "BLE_MESH_NO_LOG": false, "component-config-esp-ble-mesh-support-ble-mesh-stack-debug-log-level-ble_mesh_stack": false, "BLE_MESH_TRACE_LEVEL_NONE": false, "BLE_MESH_TRACE_LEVEL_ERROR": false, "BLE_MESH_TRACE_LEVEL_WARNING": false, "BLE_MESH_TRACE_LEVEL_INFO": false, "BLE_MESH_TRACE_LEVEL_DEBUG": false, "BLE_MESH_TRACE_LEVEL_VERBOSE": false, "BLE_MESH_STACK_TRACE_LEVEL": false, "component-config-esp-ble-mesh-support-ble-mesh-net-buf-debug-log-level-ble_mesh_net_buf": false, "BLE_MESH_NET_BUF_TRACE_LEVEL_NONE": false, "BLE_MESH_NET_BUF_TRACE_LEVEL_ERROR": false, "BLE_MESH_NET_BUF_TRACE_LEVEL_WARNING": false, "BLE_MESH_NET_BUF_TRACE_LEVEL_INFO": false, "BLE_MESH_NET_BUF_TRACE_LEVEL_DEBUG": false, "BLE_MESH_NET_BUF_TRACE_LEVEL_VERBOSE": false, "BLE_MESH_NET_BUF_TRACE_LEVEL": false, "BLE_MESH_CLIENT_MSG_TIMEOUT": false, "BLE_MESH_CFG_CLI": false, "BLE_MESH_HEALTH_CLI": false, "BLE_MESH_HEALTH_SRV": false, "BLE_MESH_BRC_CLI": false, "BLE_MESH_BRC_SRV": false, "BLE_MESH_MAX_BRIDGING_TABLE_ENTRY_COUNT": false, "BLE_MESH_BRIDGE_CRPL": false, "BLE_MESH_PRB_CLI": false, "BLE_MESH_PRB_SRV": false, "BLE_MESH_ODP_CLI": false, "BLE_MESH_ODP_SRV": false, "BLE_MESH_SRPL_CLI": false, "BLE_MESH_SRPL_SRV": false, "BLE_MESH_AGG_CLI": false, "BLE_MESH_AGG_SRV": false, "BLE_MESH_SAR_CLI": false, "BLE_MESH_SAR_SRV": false, "BLE_MESH_COMP_DATA_1": false, "BLE_MESH_COMP_DATA_128": false, "BLE_MESH_MODELS_METADATA_0": false, "BLE_MESH_MODELS_METADATA_128": false, "BLE_MESH_LCD_CLI": false, "BLE_MESH_LCD_SRV": false, "BLE_MESH_RPR_CLI": false, "BLE_MESH_RPR_CLI_PROV_SAME_TIME": false, "BLE_MESH_RPR_SRV": false, "BLE_MESH_RPR_SRV_MAX_SCANNED_ITEMS": false, "BLE_MESH_RPR_SRV_ACTIVE_SCAN": false, "BLE_MESH_RPR_SRV_MAX_EXT_SCAN": false, "BLE_MESH_DF_CLI": false, "BLE_MESH_DF_SRV": false, "BLE_MESH_MAX_DISC_TABLE_ENTRY_COUNT": false, "BLE_MESH_MAX_FORWARD_TABLE_ENTRY_COUNT": false, "BLE_MESH_MAX_DEPS_NODES_PER_PATH": false, "BLE_MESH_PATH_MONITOR_TEST": false, "BLE_MESH_SUPPORT_DIRECTED_PROXY": false, "BLE_MESH_GENERIC_ONOFF_CLI": false, "BLE_MESH_GENERIC_LEVEL_CLI": false, "BLE_MESH_GENERIC_DEF_TRANS_TIME_CLI": false, "BLE_MESH_GENERIC_POWER_ONOFF_CLI": false, "BLE_MESH_GENERIC_POWER_LEVEL_CLI": false, "BLE_MESH_GENERIC_BATTERY_CLI": false, "BLE_MESH_GENERIC_LOCATION_CLI": false, "BLE_MESH_GENERIC_PROPERTY_CLI": false, "BLE_MESH_SENSOR_CLI": false, "BLE_MESH_TIME_CLI": false, "BLE_MESH_SCENE_CLI": false, "BLE_MESH_SCHEDULER_CLI": false, "BLE_MESH_LIGHT_LIGHTNESS_CLI": false, "BLE_MESH_LIGHT_CTL_CLI": false, "BLE_MESH_LIGHT_HSL_CLI": false, "BLE_MESH_LIGHT_XYL_CLI": false, "BLE_MESH_LIGHT_LC_CLI": false, "BLE_MESH_GENERIC_SERVER": false, "BLE_MESH_SENSOR_SERVER": false, "BLE_MESH_TIME_SCENE_SERVER": false, "BLE_MESH_LIGHTING_SERVER": false, "BLE_MESH_MBT_CLI": false, "BLE_MESH_MAX_BLOB_RECEIVERS": false, "BLE_MESH_MBT_SRV": false, "BLE_MESH_IV_UPDATE_TEST": false, "BLE_MESH_DISCARD_OLD_SEQ_AUTH": false, "BLE_MESH_SELF_TEST": false, "BLE_MESH_BQB_TEST": false, "BLE_MESH_BQB_TEST_LOG": false, "BLE_MESH_TEST_AUTO_ENTER_NETWORK": false, "BLE_MESH_TEST_USE_WHITE_LIST": false, "BLE_MESH_SHELL": false, "BLE_MESH_DEBUG": false, "BLE_MESH_DEBUG_NET": false, "BLE_MESH_DEBUG_TRANS": false, "BLE_MESH_DEBUG_BEACON": false, "BLE_MESH_DEBUG_CRYPTO": false, "BLE_MESH_DEBUG_PROV": false, "BLE_MESH_DEBUG_ACCESS": false, "BLE_MESH_DEBUG_MODEL": false, "BLE_MESH_DEBUG_ADV": false, "BLE_MESH_DEBUG_LOW_POWER": false, "BLE_MESH_DEBUG_FRIEND": false, "BLE_MESH_DEBUG_PROXY": false, "BLE_MESH_EXPERIMENTAL": false, "CONSOLE_SORTED_HELP": true, "TWAI_ISR_IN_IRAM": true, "TWAI_ERRATA_FIX_BUS_OFF_REC": false, "TWAI_ERRATA_FIX_TX_INTR_LOST": false, "TWAI_ERRATA_FIX_RX_FRAME_INVALID": false, "TWAI_ERRATA_FIX_RX_FIFO_CORRUPT": false, "TWAI_ERRATA_FIX_LISTEN_ONLY_DOM": true, "ADC_DISABLE_DAC": false, "ADC_SUPPRESS_DEPRECATE_WARN": true, "ADC_SKIP_LEGACY_CONFLICT_CHECK": true, "ADC_CAL_EFUSE_TP_ENABLE": false, "ADC_CAL_EFUSE_VREF_ENABLE": false, "ADC_CAL_LUT_ENABLE": false, "ADC_CALI_SUPPRESS_DEPRECATE_WARN": true, "DAC_SUPPRESS_DEPRECATE_WARN": false, "DAC_SKIP_LEGACY_CONFLICT_CHECK": false, "MCPWM_SUPPRESS_DEPRECATE_WARN": false, "MCPWM_SKIP_LEGACY_CONFLICT_CHECK": false, "GPTIMER_SUPPRESS_DEPRECATE_WARN": true, "GPTIMER_SKIP_LEGACY_CONFLICT_CHECK": true, "RMT_SUPPRESS_DEPRECATE_WARN": true, "RMT_SKIP_LEGACY_CONFLICT_CHECK": true, "I2S_SUPPRESS_DEPRECATE_WARN": true, "I2S_SKIP_LEGACY_CONFLICT_CHECK": true, "PCNT_SUPPRESS_DEPRECATE_WARN": false, "PCNT_SKIP_LEGACY_CONFLICT_CHECK": false, "SDM_SUPPRESS_DEPRECATE_WARN": true, "SDM_SKIP_LEGACY_CONFLICT_CHECK": true, "TEMP_SENSOR_SUPPRESS_DEPRECATE_WARN": true, "TEMP_SENSOR_SKIP_LEGACY_CONFLICT_CHECK": true, "EFUSE_CUSTOM_TABLE": true, "EFUSE_CUSTOM_TABLE_FILENAME": false, "EFUSE_VIRTUAL": true, "EFUSE_VIRTUAL_KEEP_IN_FLASH": false, "EFUSE_VIRTUAL_LOG_ALL_WRITES": false, "component-config-efuse-bit-manager-coding-scheme-compatibility": false, "EFUSE_CODE_SCHEME_COMPAT_NONE": false, "EFUSE_CODE_SCHEME_COMPAT_3_4": false, "EFUSE_CODE_SCHEME_COMPAT_REPEAT": false, "EFUSE_MAX_BLK_LEN": false, "component-config-esp-tls-choose-ssl-tls-library-for-esp-tls-see-help-for-more-info-": true, "ESP_TLS_USING_MBEDTLS": true, "ESP_TLS_USING_WOLFSSL": false, "ESP_TLS_USE_SECURE_ELEMENT": false, "ESP_TLS_USE_DS_PERIPHERAL": true, "ESP_TLS_CLIENT_SESSION_TICKETS": true, "ESP_TLS_SERVER_SESSION_TICKETS": true, "ESP_TLS_SERVER_SESSION_TICKET_TIMEOUT": false, "ESP_TLS_SERVER_CERT_SELECT_HOOK": true, "ESP_TLS_SERVER_MIN_AUTH_MODE_OPTIONAL": true, "ESP_TLS_PSK_VERIFICATION": true, "ESP_TLS_INSECURE": true, "ESP_TLS_SKIP_SERVER_CERT_VERIFY": false, "ESP_WOLFSSL_SMALL_CERT_VERIFY": false, "ESP_DEBUG_WOLFSSL": false, "ADC_ONESHOT_CTRL_FUNC_IN_IRAM": true, "ADC_CONTINUOUS_ISR_IRAM_SAFE": true, "ADC_CALI_EFUSE_TP_ENABLE": false, "ADC_CALI_EFUSE_VREF_ENABLE": false, "ADC_CALI_LUT_ENABLE": false, "ADC_DISABLE_DAC_OUTPUT": false, "ADC_CONTINUOUS_FORCE_USE_ADC2_ON_C3_S3": true, "ADC_ONESHOT_FORCE_USE_ADC2_ON_C3": true, "ADC_ENABLE_DEBUG_LOG": true, "ESP_COEX_ENABLED": false, "ESP_COEX_SW_COEXIST_ENABLE": true, "ESP_COEX_EXTERNAL_COEXIST_ENABLE": false, "ESP_COEX_POWER_MANAGEMENT": true, "ESP_COEX_GPIO_DEBUG": true, "component-config-wireless-coexistence-gpio-debugging-for-coexistence-debugging-diagram": false, "ESP_COEX_GPIO_DEBUG_DIAG_GENERAL": false, "ESP_COEX_GPIO_DEBUG_DIAG_WIFI": false, "ESP_COEX_GPIO_DEBUG_IO_COUNT": false, "ESP_COEX_GPIO_DEBUG_IO_IDX0": false, "ESP_COEX_GPIO_DEBUG_IO_IDX1": false, "ESP_COEX_GPIO_DEBUG_IO_IDX2": false, "ESP_COEX_GPIO_DEBUG_IO_IDX3": false, "ESP_COEX_GPIO_DEBUG_IO_IDX4": false, "ESP_COEX_GPIO_DEBUG_IO_IDX5": false, "ESP_COEX_GPIO_DEBUG_IO_IDX6": false, "ESP_COEX_GPIO_DEBUG_IO_IDX7": false, "ESP_COEX_GPIO_DEBUG_IO_IDX8": false, "ESP_COEX_GPIO_DEBUG_IO_IDX9": false, "ESP_COEX_GPIO_DEBUG_IO_IDX10": false, "ESP_COEX_GPIO_DEBUG_IO_IDX11": false, "ESP_ERR_TO_NAME_LOOKUP": true, "ESP_ALLOW_BSS_SEG_EXTERNAL_MEMORY": false, "ANA_CMPR_ISR_IRAM_SAFE": false, "ANA_CMPR_CTRL_FUNC_IN_IRAM": false, "ANA_CMPR_ENABLE_DEBUG_LOG": false, "CAM_CTLR_MIPI_CSI_ISR_IRAM_SAFE": false, "CAM_CTLR_ISP_DVP_ISR_IRAM_SAFE": false, "CAM_CTLR_DVP_CAM_ISR_IRAM_SAFE": false, "DAC_CTRL_FUNC_IN_IRAM": false, "DAC_ISR_IRAM_SAFE": false, "DAC_ENABLE_DEBUG_LOG": false, "DAC_DMA_AUTO_16BIT_ALIGN": false, "GPIO_ESP32_SUPPORT_SWITCH_SLP_PULL": false, "GPIO_CTRL_FUNC_IN_IRAM": true, "GPTIMER_ISR_HANDLER_IN_IRAM": true, "GPTIMER_CTRL_FUNC_IN_IRAM": true, "GPTIMER_ISR_IRAM_SAFE": true, "GPTIMER_OBJ_CACHE_SAFE": false, "GPTIMER_ENABLE_DEBUG_LOG": true, "I2C_ISR_IRAM_SAFE": true, "I2C_ENABLE_DEBUG_LOG": true, "I2S_ISR_IRAM_SAFE": true, "I2S_ENABLE_DEBUG_LOG": true, "ISP_ISR_IRAM_SAFE": false, "JPEG_ENABLE_DEBUG_LOG": false, "LEDC_CTRL_FUNC_IN_IRAM": true, "MCPWM_ISR_IRAM_SAFE": false, "MCPWM_CTRL_FUNC_IN_IRAM": false, "MCPWM_ENABLE_DEBUG_LOG": false, "PARLIO_ENABLE_DEBUG_LOG": false, "PARLIO_ISR_IRAM_SAFE": false, "PCNT_CTRL_FUNC_IN_IRAM": false, "PCNT_ISR_IRAM_SAFE": false, "PCNT_ENABLE_DEBUG_LOG": false, "RMT_ISR_IRAM_SAFE": true, "RMT_RECV_FUNC_IN_IRAM": true, "RMT_ENABLE_DEBUG_LOG": true, "SDM_CTRL_FUNC_IN_IRAM": true, "SDM_ENABLE_DEBUG_LOG": true, "SPI_MASTER_IN_IRAM": true, "SPI_MASTER_ISR_IN_IRAM": true, "SPI_SLAVE_IN_IRAM": true, "SPI_SLAVE_ISR_IN_IRAM": true, "TOUCH_CTRL_FUNC_IN_IRAM": false, "TOUCH_ISR_IRAM_SAFE": false, "TOUCH_ENABLE_DEBUG_LOG": false, "TEMP_SENSOR_ENABLE_DEBUG_LOG": true, "TEMP_SENSOR_ISR_IRAM_SAFE": false, "UART_ISR_IN_IRAM": true, "USJ_ENABLE_USB_SERIAL_JTAG": true, "USJ_NO_AUTO_LS_ON_CONNECTION": false, "ETH_ENABLED": false, "ETH_USE_ESP32_EMAC": false, "component-config-ethernet-support-esp32-internal-emac-controller-phy-interface": false, "ETH_PHY_INTERFACE_RMII": false, "component-config-ethernet-support-esp32-internal-emac-controller-rmii-clock-mode": false, "ETH_RMII_CLK_INPUT": false, "ETH_RMII_CLK_OUTPUT": false, "ETH_RMII_CLK_IN_GPIO": false, "ETH_RMII_CLK_OUTPUT_GPIO0": false, "ETH_RMII_CLK_OUT_GPIO": false, "ETH_DMA_BUFFER_SIZE": false, "ETH_DMA_RX_BUFFER_NUM": false, "ETH_DMA_TX_BUFFER_NUM": false, "ETH_SOFT_FLOW_CONTROL": false, "ETH_IRAM_OPTIMIZATION": false, "ETH_USE_SPI_ETHERNET": true, "ETH_SPI_ETHERNET_DM9051": true, "ETH_SPI_ETHERNET_W5500": true, "ETH_SPI_ETHERNET_KSZ8851SNL": true, "ETH_USE_OPENETH": true, "ETH_OPENETH_DMA_RX_BUFFER_NUM": false, "ETH_OPENETH_DMA_TX_BUFFER_NUM": false, "ETH_TRANSMIT_MUTEX": true, "ESP_EVENT_LOOP_PROFILING": true, "ESP_EVENT_POST_FROM_ISR": true, "ESP_EVENT_POST_FROM_IRAM_ISR": true, "ESP_GDBSTUB_ENABLED": false, "ESP_SYSTEM_GDBSTUB_RUNTIME": true, "ESP_GDBSTUB_SUPPORT_TASKS": true, "ESP_GDBSTUB_MAX_TASKS": true, "ESPHID_TASK_SIZE_BT": true, "ESPHID_TASK_SIZE_BLE": true, "ESP_HTTP_CLIENT_ENABLE_HTTPS": true, "ESP_HTTP_CLIENT_ENABLE_BASIC_AUTH": true, "ESP_HTTP_CLIENT_ENABLE_DIGEST_AUTH": true, "ESP_HTTP_CLIENT_ENABLE_CUSTOM_TRANSPORT": true, "HTTPD_MAX_REQ_HDR_LEN": true, "HTTPD_MAX_URI_LEN": true, "HTTPD_ERR_RESP_NO_DELAY": true, "HTTPD_PURGE_BUF_LEN": true, "HTTPD_LOG_PURGE_DATA": true, "HTTPD_WS_SUPPORT": true, "HTTPD_QUEUE_WORK_BLOCKING": true, "ESP_HTTPS_OTA_DECRYPT_CB": true, "ESP_HTTPS_OTA_ALLOW_HTTP": true, "ESP_HTTPS_SERVER_ENABLE": true, "component-config-hardware-settings-chip-revision-minimum-supported-esp32-c3-revision": true, "ESP32C3_REV_MIN_0": true, "ESP32C3_REV_MIN_1": true, "ESP32C3_REV_MIN_2": true, "ESP32C3_REV_MIN_3": true, "ESP32C3_REV_MIN_4": true, "ESP32C3_REV_MIN_101": true, "ESP32C3_REV_MIN_FULL": false, "ESP_REV_MIN_FULL": false, "ESP32C3_REV_MAX_FULL": false, "ESP_REV_MAX_FULL": false, "ESP_EFUSE_BLOCK_REV_MIN_FULL": true, "ESP_EFUSE_BLOCK_REV_MAX_FULL": false, "ESP_REV_NEW_CHIP_TEST": false, "ESP_MAC_ADDR_UNIVERSE_WIFI_STA": false, "ESP_MAC_ADDR_UNIVERSE_WIFI_AP": false, "ESP_MAC_ADDR_UNIVERSE_BT": false, "ESP_MAC_ADDR_UNIVERSE_ETH": false, "ESP_MAC_ADDR_UNIVERSE_IEEE802154": false, "ESP_MAC_UNIVERSAL_MAC_ADDRESSES_ONE": false, "ESP_MAC_UNIVERSAL_MAC_ADDRESSES_TWO": false, "ESP_MAC_UNIVERSAL_MAC_ADDRESSES_FOUR": false, "ESP_MAC_UNIVERSAL_MAC_ADDRESSES": false, "component-config-hardware-settings-mac-config-number-of-universally-administered-by-ieee-mac-address": true, "ESP32C3_UNIVERSAL_MAC_ADDRESSES_TWO": true, "ESP32C3_UNIVERSAL_MAC_ADDRESSES_FOUR": true, "ESP32C3_UNIVERSAL_MAC_ADDRESSES": false, "ESP_MAC_IGNORE_MAC_CRC_ERROR": false, "ESP_MAC_USE_CUSTOM_MAC_AS_BASE_MAC": true, "ESP_SLEEP_POWER_DOWN_FLASH": true, "ESP_SLEEP_FLASH_LEAKAGE_WORKAROUND": true, "ESP_SLEEP_PSRAM_LEAKAGE_WORKAROUND": false, "ESP_SLEEP_MSPI_NEED_ALL_IO_PU": true, "ESP_SLEEP_RTC_BUS_ISO_WORKAROUND": false, "ESP_SLEEP_GPIO_RESET_WORKAROUND": true, "ESP_SLEEP_WAIT_FLASH_READY_EXTRA_DELAY": true, "ESP_SLEEP_CACHE_SAFE_ASSERTION": true, "ESP_SLEEP_DEBUG": true, "ESP_SLEEP_GPIO_ENABLE_INTERNAL_RESISTORS": true, "ESP_SLEEP_EVENT_CALLBACKS": false, "component-config-hardware-settings-rtc-clock-config-rtc-clock-source": true, "RTC_CLK_SRC_INT_RC": true, "RTC_CLK_SRC_EXT_CRYS": true, "RTC_CLK_SRC_EXT_OSC": true, "RTC_CLK_SRC_INT_8MD256": true, "RTC_CLK_CAL_CYCLES": true, "PERIPH_CTRL_FUNC_IN_IRAM": true, "ETM_ENABLE_DEBUG_LOG": false, "GDMA_CTRL_FUNC_IN_IRAM": true, "GDMA_ISR_IRAM_SAFE": true, "GDMA_ENABLE_DEBUG_LOG": true, "DW_GDMA_CTRL_FUNC_IN_IRAM": false, "DW_GDMA_SETTER_FUNC_IN_IRAM": false, "DW_GDMA_GETTER_FUNC_IN_IRAM": false, "DW_GDMA_ISR_IRAM_SAFE": false, "DW_GDMA_OBJ_DRAM_SAFE": false, "DW_GDMA_ENABLE_DEBUG_LOG": false, "DMA2D_OPERATION_FUNC_IN_IRAM": false, "DMA2D_ISR_IRAM_SAFE": false, "component-config-hardware-settings-main-xtal-config-main-xtal-frequency": true, "XTAL_FREQ_24": false, "XTAL_FREQ_26": false, "XTAL_FREQ_32": false, "XTAL_FREQ_40": true, "XTAL_FREQ_48": false, "XTAL_FREQ_AUTO": false, "XTAL_FREQ": false, "ESP_CRYPTO_DPA_PROTECTION_AT_STARTUP": false, "component-config-hardware-settings-crypto-dpa-protection-enable-crypto-dpa-protection-at-startup-dpa-protection-level": false, "ESP_CRYPTO_DPA_PROTECTION_LEVEL_LOW": false, "ESP_CRYPTO_DPA_PROTECTION_LEVEL_MEDIUM": false, "ESP_CRYPTO_DPA_PROTECTION_LEVEL_HIGH": false, "ESP_CRYPTO_DPA_PROTECTION_LEVEL": false, "ESP_CRYPTO_FORCE_ECC_CONSTANT_TIME_POINT_MUL": false, "ESP_BRINGUP_BYPASS_CPU_CLK_SETTING": false, "ESP_BRINGUP_BYPASS_RANDOM_SETTING": false, "ESP_SPI_BUS_LOCK_ISR_FUNCS_IN_IRAM": false, "ESP_SPI_BUS_LOCK_FUNCS_IN_IRAM": false, "LCD_ENABLE_DEBUG_LOG": true, "LCD_RGB_ISR_IRAM_SAFE": false, "LCD_RGB_RESTART_IN_VSYNC": false, "LCD_DSI_ISR_IRAM_SAFE": false, "ESP_NETIF_IP_LOST_TIMER_INTERVAL": true, "component-config-esp-netif-adapter-tcp-ip-stack-library": true, "ESP_NETIF_TCPIP_LWIP": true, "ESP_NETIF_LOOPBACK": true, "ESP_NETIF_USES_TCPIP_WITH_BSD_API": false, "ESP_NETIF_RECEIVE_REPORT_ERRORS": true, "ESP_NETIF_L2_TAP": true, "ESP_NETIF_L2_TAP_MAX_FDS": false, "ESP_NETIF_L2_TAP_RX_QUEUE_SIZE": false, "ESP_NETIF_BRIDGE_EN": true, "ESP_NETIF_SET_DNS_PER_DEFAULT_NETIF": true, "ESP_PARTITION_ENABLE_STATS": false, "ESP_PHY_ENABLED": false, "ESP_PHY_CALIBRATION_AND_DATA_STORAGE": true, "ESP_PHY_INIT_DATA_IN_PARTITION": true, "ESP_PHY_DEFAULT_INIT_IF_INVALID": false, "ESP_PHY_MULTIPLE_INIT_DATA_BIN": false, "ESP_PHY_MULTIPLE_INIT_DATA_BIN_EMBED": false, "ESP_PHY_INIT_DATA_ERROR": false, "ESP_PHY_MAX_WIFI_TX_POWER": true, "ESP_PHY_MAX_TX_POWER": false, "ESP_PHY_MAC_BB_PD": false, "ESP_PHY_REDUCE_TX_POWER": true, "ESP_PHY_ENABLE_USB": true, "ESP_PHY_ENABLE_CERT_TEST": true, "component-config-phy-calibration-mode": true, "ESP_PHY_RF_CAL_PARTIAL": true, "ESP_PHY_RF_CAL_NONE": true, "ESP_PHY_RF_CAL_FULL": true, "ESP_PHY_CALIBRATION_MODE": false, "ESP_PHY_IMPROVE_RX_11B": false, "ESP_PHY_PLL_TRACK_DEBUG": true, "ESP_PHY_RECORD_USED_TIME": true, "PM_ENABLE": true, "PM_DFS_INIT_AUTO": false, "PM_PROFILING": false, "PM_TRACE": false, "PM_SLP_IRAM_OPT": true, "PM_RTOS_IDLE_OPT": false, "PM_SLP_DISABLE_GPIO": false, "PM_SLP_DEFAULT_PARAMS_OPT": false, "PM_CHECK_SLEEP_RETENTION_FRAME": false, "PM_LIGHTSLEEP_RTC_OSC_CAL_INTERVAL": false, "PM_POWER_DOWN_CPU_IN_LIGHT_SLEEP": true, "PM_RESTORE_CACHE_TAGMEM_AFTER_LIGHT_SLEEP": false, "PM_POWER_DOWN_PERIPHERAL_IN_LIGHT_SLEEP": false, "PM_UPDATE_CCOMPARE_HLI_WORKAROUND": false, "PM_LIGHT_SLEEP_CALLBACKS": false, "RINGBUF_PLACE_FUNCTIONS_INTO_FLASH": true, "RINGBUF_PLACE_ISR_FUNCTIONS_INTO_FLASH": false, "component-config-esp-system-settings-cpu-frequency": true, "ESP_DEFAULT_CPU_FREQ_MHZ_40": false, "ESP_DEFAULT_CPU_FREQ_MHZ_80": true, "ESP_DEFAULT_CPU_FREQ_MHZ_160": true, "ESP_DEFAULT_CPU_FREQ_MHZ": false, "component-config-esp-system-settings-panic-handler-behaviour": true, "ESP_SYSTEM_PANIC_PRINT_HALT": true, "ESP_SYSTEM_PANIC_PRINT_REBOOT": true, "ESP_SYSTEM_PANIC_SILENT_REBOOT": true, "ESP_SYSTEM_PANIC_GDBSTUB": true, "ESP_SYSTEM_PANIC_REBOOT_DELAY_SECONDS": true, "ESP_SYSTEM_SINGLE_CORE_MODE": false, "ESP_SYSTEM_RTC_EXT_XTAL": false, "ESP_SYSTEM_RTC_EXT_OSC": false, "ESP_SYSTEM_RTC_EXT_XTAL_BOOTSTRAP_CYCLES": false, "ESP_SYSTEM_RTC_FAST_MEM_AS_HEAP_DEPCHECK": false, "ESP_SYSTEM_ALLOW_RTC_FAST_MEM_AS_HEAP": true, "ESP_SYSTEM_USE_EH_FRAME": true, "ESP_SYSTEM_PMP_IDRAM_SPLIT": false, "ESP_SYSTEM_MEMPROT_FEATURE": true, "ESP_SYSTEM_MEMPROT_FEATURE_LOCK": true, "ESP_SYSTEM_EVENT_QUEUE_SIZE": true, "ESP_SYSTEM_EVENT_TASK_STACK_SIZE": true, "ESP_MAIN_TASK_STACK_SIZE": true, "component-config-esp-system-settings-main-task-core-affinity": true, "ESP_MAIN_TASK_AFFINITY_CPU0": true, "ESP_MAIN_TASK_AFFINITY_CPU1": false, "ESP_MAIN_TASK_AFFINITY_NO_AFFINITY": true, "ESP_MAIN_TASK_AFFINITY": false, "ESP_MINIMAL_SHARED_STACK_SIZE": true, "component-config-esp-system-settings-channel-for-console-output": true, "ESP_CONSOLE_UART_DEFAULT": true, "ESP_CONSOLE_USB_CDC": false, "ESP_CONSOLE_USB_SERIAL_JTAG": true, "ESP_CONSOLE_UART_CUSTOM": true, "ESP_CONSOLE_NONE": true, "component-config-esp-system-settings-channel-for-console-secondary-output": true, "ESP_CONSOLE_SECONDARY_NONE": true, "ESP_CONSOLE_SECONDARY_USB_SERIAL_JTAG": true, "ESP_CONSOLE_USB_SERIAL_JTAG_ENABLED": false, "ESP_CONSOLE_UART": false, "component-config-esp-system-settings-uart-peripheral-to-use-for-console-output-0-1-": false, "ESP_CONSOLE_UART_CUSTOM_NUM_0": false, "ESP_CONSOLE_UART_CUSTOM_NUM_1": false, "ESP_CONSOLE_UART_NUM": false, "ESP_CONSOLE_ROM_SERIAL_PORT_NUM": false, "ESP_CONSOLE_UART_TX_GPIO": false, "ESP_CONSOLE_UART_RX_GPIO": false, "ESP_CONSOLE_UART_BAUDRATE": false, "ESP_CONSOLE_USB_CDC_RX_BUF_SIZE": false, "ESP_CONSOLE_USB_CDC_SUPPORT_ETS_PRINTF": false, "ESP_INT_WDT": true, "ESP_INT_WDT_TIMEOUT_MS": true, "ESP_INT_WDT_CHECK_CPU1": false, "ESP_TASK_WDT_EN": true, "ESP_TASK_WDT_USE_ESP_TIMER": false, "ESP_TASK_WDT_INIT": true, "ESP_TASK_WDT_PANIC": true, "ESP_TASK_WDT_TIMEOUT_S": true, "ESP_TASK_WDT_CHECK_IDLE_TASK_CPU0": true, "ESP_TASK_WDT_CHECK_IDLE_TASK_CPU1": false, "ESP_XT_WDT": false, "ESP_XT_WDT_TIMEOUT": false, "ESP_XT_WDT_BACKUP_CLK_ENABLE": false, "ESP_PANIC_HANDLER_IRAM": true, "ESP_DEBUG_STUBS_ENABLE": true, "ESP_DEBUG_OCDAWARE": true, "component-config-esp-system-settings-interrupt-level-to-use-for-interrupt-watchdog-and-other-system-checks": true, "ESP_SYSTEM_CHECK_INT_LEVEL_5": false, "ESP_SYSTEM_CHECK_INT_LEVEL_4": true, "ESP_BROWNOUT_DET": true, "component-config-esp-system-settings-brownout-detector-hardware-brownout-detect-reset-brownout-voltage-level": true, "ESP_BROWNOUT_DET_LVL_SEL_7": true, "ESP_BROWNOUT_DET_LVL_SEL_6": true, "ESP_BROWNOUT_DET_LVL_SEL_5": true, "ESP_BROWNOUT_DET_LVL_SEL_4": true, "ESP_BROWNOUT_DET_LVL_SEL_3": true, "ESP_BROWNOUT_DET_LVL_SEL_2": true, "ESP_BROWNOUT_DET_LVL": false, "ESP_SYSTEM_BROWNOUT_INTR": false, "ESP_SYSTEM_HW_STACK_GUARD": true, "ESP_SYSTEM_BBPLL_RECALIB": false, "ESP_SYSTEM_HW_PC_RECORD": true, "ESP_IPC_TASK_STACK_SIZE": true, "ESP_IPC_USES_CALLERS_PRIORITY": false, "ESP_IPC_ISR_ENABLE": false, "ESP_TIMER_PROFILING": true, "ESP_TIME_FUNCS_USE_RTC_TIMER": false, "ESP_TIME_FUNCS_USE_ESP_TIMER": false, "ESP_TIME_FUNCS_USE_NONE": false, "ESP_TIMER_TASK_STACK_SIZE": true, "ESP_TIMER_INTERRUPT_LEVEL": true, "ESP_TIMER_SHOW_EXPERIMENTAL": true, "ESP_TIMER_TASK_AFFINITY": false, "component-config-esp-timer-high-resolution-timer--esp_timer-task-core-affinity": true, "ESP_TIMER_TASK_AFFINITY_CPU0": true, "ESP_TIMER_TASK_AFFINITY_CPU1": false, "ESP_TIMER_TASK_AFFINITY_NO_AFFINITY": false, "component-config-esp-timer-high-resolution-timer--timer-interrupt-core-affinity": true, "ESP_TIMER_ISR_AFFINITY_CPU0": true, "ESP_TIMER_ISR_AFFINITY_CPU1": false, "ESP_TIMER_ISR_AFFINITY_NO_AFFINITY": false, "ESP_TIMER_SUPPORTS_ISR_DISPATCH_METHOD": true, "ESP_TIMER_IMPL_TG0_LAC": false, "ESP_TIMER_IMPL_SYSTIMER": false, "ESP_WIFI_ENABLED": false, "ESP_HOST_WIFI_ENABLED": false, "ESP_WIFI_CONTROLLER_TARGET": false, "ESP_WIFI_TARGET_ESP32": false, "ESP_WIFI_STATIC_RX_BUFFER_NUM": true, "ESP_WIFI_DYNAMIC_RX_BUFFER_NUM": true, "component-config-wi-fi-type-of-wifi-tx-buffers": true, "ESP_WIFI_STATIC_TX_BUFFER": true, "ESP_WIFI_DYNAMIC_TX_BUFFER": true, "ESP_WIFI_TX_BUFFER_TYPE": false, "ESP_WIFI_STATIC_TX_BUFFER_NUM": false, "ESP_WIFI_CACHE_TX_BUFFER_NUM": false, "ESP_WIFI_DYNAMIC_TX_BUFFER_NUM": true, "component-config-wi-fi-type-of-wifi-rx-mgmt-buffers": true, "ESP_WIFI_STATIC_RX_MGMT_BUFFER": true, "ESP_WIFI_DYNAMIC_RX_MGMT_BUFFER": true, "ESP_WIFI_DYNAMIC_RX_MGMT_BUF": false, "ESP_WIFI_RX_MGMT_BUF_NUM_DEF": true, "ESP_WIFI_CSI_ENABLED": true, "ESP_WIFI_AMPDU_TX_ENABLED": true, "ESP_WIFI_TX_BA_WIN": true, "ESP_WIFI_AMPDU_RX_ENABLED": true, "ESP_WIFI_RX_BA_WIN": true, "ESP_WIFI_AMSDU_TX_ENABLED": false, "ESP_WIFI_NVS_ENABLED": true, "component-config-wi-fi-wifi-task-core-id": false, "ESP_WIFI_TASK_PINNED_TO_CORE_0": false, "ESP_WIFI_TASK_PINNED_TO_CORE_1": false, "ESP_WIFI_SOFTAP_BEACON_MAX_LEN": true, "ESP_WIFI_MGMT_SBUF_NUM": true, "ESP_WIFI_IRAM_OPT": true, "ESP_WIFI_EXTRA_IRAM_OPT": true, "ESP_WIFI_RX_IRAM_OPT": true, "ESP_WIFI_ENABLE_WPA3_SAE": true, "ESP_WIFI_ENABLE_SAE_PK": true, "ESP_WIFI_SOFTAP_SAE_SUPPORT": true, "ESP_WIFI_ENABLE_WPA3_OWE_STA": true, "ESP_WIFI_SLP_IRAM_OPT": true, "ESP_WIFI_SLP_DEFAULT_MIN_ACTIVE_TIME": true, "ESP_WIFI_SLP_DEFAULT_MAX_ACTIVE_TIME": true, "ESP_WIFI_SLP_DEFAULT_WAIT_BROADCAST_DATA_TIME": true, "ESP_WIFI_FTM_ENABLE": true, "ESP_WIFI_FTM_INITIATOR_SUPPORT": false, "ESP_WIFI_FTM_RESPONDER_SUPPORT": false, "ESP_WIFI_STA_DISCONNECTED_PM_ENABLE": true, "ESP_WIFI_GCMP_SUPPORT": true, "ESP_WIFI_GMAC_SUPPORT": true, "ESP_WIFI_SOFTAP_SUPPORT": true, "ESP_WIFI_ENHANCED_LIGHT_SLEEP": false, "ESP_WIFI_SLP_BEACON_LOST_OPT": true, "ESP_WIFI_SLP_BEACON_LOST_TIMEOUT": false, "ESP_WIFI_SLP_BEACON_LOST_THRESHOLD": false, "ESP_WIFI_SLP_PHY_ON_DELTA_EARLY_TIME": false, "ESP_WIFI_SLP_PHY_OFF_DELTA_TIMEOUT_TIME": false, "ESP_WIFI_ESPNOW_MAX_ENCRYPT_NUM": true, "ESP_WIFI_NAN_ENABLE": false, "ESP_WIFI_MBEDTLS_CRYPTO": true, "ESP_WIFI_MBEDTLS_TLS_CLIENT": true, "ESP_WIFI_EAP_TLS1_3": false, "ESP_WIFI_WAPI_PSK": true, "ESP_WIFI_SUITE_B_192": true, "ESP_WIFI_11KV_SUPPORT": true, "ESP_WIFI_RRM_SUPPORT": false, "ESP_WIFI_WNM_SUPPORT": false, "ESP_WIFI_SCAN_CACHE": false, "ESP_WIFI_MBO_SUPPORT": true, "ESP_WIFI_ENABLE_ROAMING_APP": false, "ESP_WIFI_ROAMING_LOW_RSSI_ROAMING": false, "ESP_WIFI_ROAMING_LOW_RSSI_THRESHOLD": false, "ESP_WIFI_ROAMING_LOW_RSSI_OFFSET": false, "ESP_WIFI_ROAMING_PERIODIC_SCAN_MONITOR": false, "ESP_WIFI_ROAMING_PERIODIC_SCAN_THRESHOLD": false, "ESP_WIFI_ROAMING_SCAN_MONITOR_INTERVAL": false, "ESP_WIFI_ROAMING_SCAN_ROAM_RSSI_DIFF": false, "ESP_WIFI_ROAMING_LEGACY_ROAMING": false, "ESP_WIFI_ROAMING_NETWORK_ASSISTED_ROAM": false, "ESP_WIFI_NETWORK_ASSISTED_ROAMING_RETRY_COUNT": false, "ESP_WIFI_ROAMING_SCAN_MIN_SCAN_TIME": false, "ESP_WIFI_ROAMING_SCAN_MAX_SCAN_TIME": false, "ESP_WIFI_ROAMING_HOME_CHANNEL_DWELL_TIME": false, "ESP_WIFI_ROAMING_SCAN_CHAN_LIST": false, "ESP_WIFI_ROAMING_SCAN_EXPIRY_WINDOW": false, "ESP_WIFI_ROAMING_MAX_CANDIDATES": false, "ESP_WIFI_ROAMING_BACKOFF_TIME": false, "ESP_WIFI_ROAMING_PERIODIC_RRM_MONITORING": false, "ESP_WIFI_ROAMING_RRM_MONITOR_TIME": false, "ESP_WIFI_ROAMING_RRM_MONITOR_THRESHOLD": false, "ESP_WIFI_DPP_SUPPORT": true, "ESP_WIFI_11R_SUPPORT": true, "ESP_WIFI_WPS_SOFTAP_REGISTRAR": true, "ESP_WIFI_ENABLE_WIFI_TX_STATS": false, "ESP_WIFI_ENABLE_WIFI_RX_STATS": false, "ESP_WIFI_ENABLE_WIFI_RX_MU_STATS": false, "ESP_WIFI_TX_HETB_QUEUE_NUM": false, "ESP_WIFI_ENABLE_DUMP_HESIGB": false, "ESP_WIFI_ENABLE_DUMP_MU_CFO": false, "ESP_WIFI_ENABLE_DUMP_CTRL_NDPA": false, "ESP_WIFI_ENABLE_DUMP_CTRL_BFRP": false, "ESP_WIFI_WPS_STRICT": true, "ESP_WIFI_WPS_PASSPHRASE": true, "ESP_WIFI_DEBUG_PRINT": true, "ESP_WIFI_TESTING_OPTIONS": true, "ESP_WIFI_ENTERPRISE_SUPPORT": true, "ESP_WIFI_ENT_FREE_DYNAMIC_BUFFER": true, "component-config-core-dump-data-destination": true, "ESP_COREDUMP_ENABLE_TO_FLASH": true, "ESP_COREDUMP_ENABLE_TO_UART": true, "ESP_COREDUMP_ENABLE_TO_NONE": true, "component-config-core-dump-core-dump-data-format": false, "ESP_COREDUMP_DATA_FORMAT_BIN": false, "ESP_COREDUMP_DATA_FORMAT_ELF": false, "component-config-core-dump-core-dump-data-integrity-check": false, "ESP_COREDUMP_CHECKSUM_CRC32": false, "ESP_COREDUMP_CHECKSUM_SHA256": false, "ESP_COREDUMP_CAPTURE_DRAM": false, "ESP_COREDUMP_CHECK_BOOT": false, "ESP_COREDUMP_ENABLE": false, "ESP_COREDUMP_LOGS": false, "ESP_COREDUMP_MAX_TASKS_NUM": false, "ESP_COREDUMP_UART_DELAY": false, "ESP_COREDUMP_FLASH_NO_OVERWRITE": false, "ESP_COREDUMP_USE_STACK_SIZE": false, "ESP_COREDUMP_STACK_SIZE": false, "ESP_COREDUMP_SUMMARY_STACKDUMP_SIZE": false, "component-config-core-dump-handling-of-uart-core-dumps-in-idf-monitor": false, "ESP_COREDUMP_DECODE_INFO": false, "ESP_COREDUMP_DECODE_DISABLE": false, "ESP_COREDUMP_DECODE": false, "FATFS_VOLUME_COUNT": true, "component-config-fat-filesystem-support-long-filename-support": true, "FATFS_LFN_NONE": true, "FATFS_LFN_HEAP": true, "FATFS_LFN_STACK": true, "component-config-fat-filesystem-support-sector-size": true, "FATFS_SECTOR_512": true, "FATFS_SECTOR_4096": true, "component-config-fat-filesystem-support-oem-code-page": true, "FATFS_CODEPAGE_DYNAMIC": true, "FATFS_CODEPAGE_437": true, "FATFS_CODEPAGE_720": true, "FATFS_CODEPAGE_737": true, "FATFS_CODEPAGE_771": true, "FATFS_CODEPAGE_775": true, "FATFS_CODEPAGE_850": true, "FATFS_CODEPAGE_852": true, "FATFS_CODEPAGE_855": true, "FATFS_CODEPAGE_857": true, "FATFS_CODEPAGE_860": true, "FATFS_CODEPAGE_861": true, "FATFS_CODEPAGE_862": true, "FATFS_CODEPAGE_863": true, "FATFS_CODEPAGE_864": true, "FATFS_CODEPAGE_865": true, "FATFS_CODEPAGE_866": true, "FATFS_CODEPAGE_869": true, "FATFS_CODEPAGE_932": true, "FATFS_CODEPAGE_936": true, "FATFS_CODEPAGE_949": true, "FATFS_CODEPAGE_950": true, "FATFS_CODEPAGE": false, "FATFS_MAX_LFN": false, "component-config-fat-filesystem-support-api-character-encoding": false, "FATFS_API_ENCODING_ANSI_OEM": false, "FATFS_API_ENCODING_UTF_8": false, "FATFS_FS_LOCK": true, "FATFS_TIMEOUT_MS": true, "FATFS_PER_FILE_CACHE": true, "FATFS_ALLOC_PREFER_EXTRAM": false, "FATFS_USE_FASTSEEK": true, "FATFS_FAST_SEEK_BUFFER_SIZE": false, "FATFS_VFS_FSTAT_BLKSIZE": true, "FATFS_IMMEDIATE_FSYNC": true, "FATFS_USE_LABEL": true, "FATFS_LINK_LOCK": true, "FATFS_USE_DYN_BUFFERS": false, "FREERTOS_SMP": true, "FREERTOS_UNICORE": true, "FREERTOS_HZ": true, "FREERTOS_OPTIMIZED_SCHEDULER": true, "component-config-freertos-kernel-configcheck_for_stack_overflow": true, "FREERTOS_CHECK_STACKOVERFLOW_NONE": true, "FREERTOS_CHECK_STACKOVERFLOW_PTRVAL": true, "FREERTOS_CHECK_STACKOVERFLOW_CANARY": true, "FREERTOS_THREAD_LOCAL_STORAGE_POINTERS": true, "FREERTOS_IDLE_TASK_STACKSIZE": true, "FREERTOS_USE_IDLE_HOOK": true, "FREERTOS_USE_PASSIVE_IDLE_HOOK": false, "FREERTOS_USE_TICK_HOOK": true, "FREERTOS_MAX_TASK_NAME_LEN": true, "FREERTOS_ENABLE_BACKWARD_COMPATIBILITY": true, "FREERTOS_TIMER_SERVICE_TASK_NAME": true, "component-config-freertos-kernel-configtimer_service_task_core_affinity": true, "FREERTOS_TIMER_TASK_AFFINITY_CPU0": true, "FREERTOS_TIMER_TASK_AFFINITY_CPU1": false, "FREERTOS_TIMER_TASK_NO_AFFINITY": true, "FREERTOS_TIMER_SERVICE_TASK_CORE_AFFINITY": false, "FREERTOS_TIMER_TASK_PRIORITY": true, "FREERTOS_TIMER_TASK_STACK_DEPTH": true, "FREERTOS_TIMER_QUEUE_LENGTH": true, "FREERTOS_QUEUE_REGISTRY_SIZE": true, "FREERTOS_TASK_NOTIFICATION_ARRAY_ENTRIES": true, "FREERTOS_USE_TRACE_FACILITY": true, "FREERTOS_USE_STATS_FORMATTING_FUNCTIONS": false, "FREERTOS_USE_LIST_DATA_INTEGRITY_CHECK_BYTES": true, "FREERTOS_VTASKLIST_INCLUDE_COREID": false, "FREERTOS_GENERATE_RUN_TIME_STATS": true, "component-config-freertos-kernel-configgenerate_run_time_stats-configrun_time_counter_type": false, "FREERTOS_RUN_TIME_COUNTER_TYPE_U32": false, "FREERTOS_RUN_TIME_COUNTER_TYPE_U64": false, "FREERTOS_USE_TICKLESS_IDLE": false, "FREERTOS_IDLE_TIME_BEFORE_SLEEP": false, "FREERTOS_USE_APPLICATION_TASK_TAG": true, "FREERTOS_TASK_FUNCTION_WRAPPER": true, "FREERTOS_WATCHPOINT_END_OF_STACK": true, "FREERTOS_TLSP_DELETION_CALLBACKS": true, "FREERTOS_TASK_PRE_DELETION_HOOK": true, "FREERTOS_ENABLE_STATIC_TASK_CLEAN_UP": true, "FREERTOS_CHECK_MUTEX_GIVEN_BY_OWNER": true, "FREERTOS_ISR_STACKSIZE": true, "FREERTOS_INTERRUPT_BACKTRACE": true, "FREERTOS_FPU_IN_ISR": false, "FREERTOS_TICK_SUPPORT_CORETIMER": false, "FREERTOS_TICK_SUPPORT_SYSTIMER": false, "component-config-freertos-port-tick-timer-source-xtensa-only-": true, "FREERTOS_CORETIMER_0": false, "FREERTOS_CORETIMER_1": false, "FREERTOS_CORETIMER_SYSTIMER_LVL1": true, "FREERTOS_CORETIMER_SYSTIMER_LVL3": true, "FREERTOS_SYSTICK_USES_SYSTIMER": false, "FREERTOS_SYSTICK_USES_CCOUNT": false, "component-config-freertos-port-choose-the-clock-source-for-run-time-stats": false, "FREERTOS_RUN_TIME_STATS_USING_ESP_TIMER": false, "FREERTOS_RUN_TIME_STATS_USING_CPU_CLK": false, "FREERTOS_PLACE_FUNCTIONS_INTO_FLASH": true, "FREERTOS_CHECK_PORT_CRITICAL_COMPLIANCE": true, "FREERTOS_PORT": false, "FREERTOS_NO_AFFINITY": false, "FREERTOS_SUPPORT_STATIC_ALLOCATION": false, "FREERTOS_DEBUG_OCDAWARE": false, "FREERTOS_ENABLE_TASK_SNAPSHOT": false, "FREERTOS_PLACE_SNAPSHOT_FUNS_INTO_FLASH": false, "FREERTOS_NUMBER_OF_CORES": false, "component-config-hardware-abstraction-layer-hal-and-low-level-ll--default-hal-assertion-level": true, "HAL_ASSERTION_EQUALS_SYSTEM": true, "HAL_ASSERTION_DISABLE": true, "HAL_ASSERTION_SILENT": true, "HAL_ASSERTION_ENABLE": true, "HAL_DEFAULT_ASSERTION_LEVEL": false, "component-config-hardware-abstraction-layer-hal-and-low-level-ll--hal-layer-log-verbosity": false, "HAL_LOG_LEVEL_NONE": false, "HAL_LOG_LEVEL_ERROR": false, "HAL_LOG_LEVEL_WARN": false, "HAL_LOG_LEVEL_INFO": false, "HAL_LOG_LEVEL_DEBUG": false, "HAL_LOG_LEVEL_VERBOSE": false, "HAL_LOG_LEVEL": false, "HAL_SYSTIMER_USE_ROM_IMPL": false, "HAL_WDT_USE_ROM_IMPL": false, "HAL_SPI_MASTER_FUNC_IN_IRAM": false, "HAL_SPI_SLAVE_FUNC_IN_IRAM": false, "HAL_ECDSA_GEN_SIG_CM": false, "component-config-heap-memory-debugging-heap-corruption-detection": true, "HEAP_POISONING_DISABLED": true, "HEAP_POISONING_LIGHT": true, "HEAP_POISONING_COMPREHENSIVE": true, "component-config-heap-memory-debugging-heap-tracing": true, "HEAP_TRACING_OFF": true, "HEAP_TRACING_STANDALONE": true, "HEAP_TRACING_TOHOST": true, "HEAP_TRACING": false, "HEAP_TRACING_STACK_DEPTH": false, "HEAP_USE_HOOKS": true, "HEAP_TASK_TRACKING": true, "HEAP_TRACE_HASH_MAP": false, "HEAP_TRACE_HASH_MAP_IN_EXT_RAM": false, "HEAP_TRACE_HASH_MAP_SIZE": false, "HEAP_ABORT_WHEN_ALLOCATION_FAILS": true, "HEAP_TLSF_USE_ROM_IMPL": false, "HEAP_PLACE_FUNCTION_INTO_FLASH": true, "IEEE802154_ENABLED": false, "IEEE802154_RX_BUFFER_SIZE": false, "component-config-ieee-802-15-4-ieee802154-enable-clear-channel-assessment-cca-mode": false, "IEEE802154_CCA_CARRIER": false, "IEEE802154_CCA_ED": false, "IEEE802154_CCA_CARRIER_OR_ED": false, "IEEE802154_CCA_CARRIER_AND_ED": false, "IEEE802154_CCA_MODE": false, "IEEE802154_CCA_THRESHOLD": false, "IEEE802154_PENDING_TABLE_SIZE": false, "IEEE802154_MULTI_PAN_ENABLE": false, "IEEE802154_TIMING_OPTIMIZATION": false, "IEEE802154_SLEEP_ENABLE": false, "IEEE802154_DEBUG": false, "IEEE802154_RX_BUFFER_STATISTIC": false, "IEEE802154_ASSERT": false, "IEEE802154_RECORD": false, "IEEE802154_RECORD_EVENT": false, "IEEE802154_RECORD_EVENT_SIZE": false, "IEEE802154_RECORD_STATE": false, "IEEE802154_RECORD_STATE_SIZE": false, "IEEE802154_RECORD_CMD": false, "IEEE802154_RECORD_CMD_SIZE": false, "IEEE802154_RECORD_ABORT": false, "IEEE802154_RECORD_ABORT_SIZE": false, "IEEE802154_TXRX_STATISTIC": false, "component-config-log-output-default-log-verbosity": true, "LOG_DEFAULT_LEVEL_NONE": true, "LOG_DEFAULT_LEVEL_ERROR": true, "LOG_DEFAULT_LEVEL_WARN": true, "LOG_DEFAULT_LEVEL_INFO": true, "LOG_DEFAULT_LEVEL_DEBUG": true, "LOG_DEFAULT_LEVEL_VERBOSE": true, "LOG_DEFAULT_LEVEL": false, "component-config-log-output-maximum-log-verbosity": true, "LOG_MAXIMUM_EQUALS_DEFAULT": true, "LOG_MAXIMUM_LEVEL_ERROR": false, "LOG_MAXIMUM_LEVEL_WARN": false, "LOG_MAXIMUM_LEVEL_INFO": false, "LOG_MAXIMUM_LEVEL_DEBUG": true, "LOG_MAXIMUM_LEVEL_VERBOSE": true, "LOG_MAXIMUM_LEVEL": false, "LOG_MASTER_LEVEL": true, "LOG_COLORS": true, "component-config-log-output-log-timestamps": true, "LOG_TIMESTAMP_SOURCE_RTOS": true, "LOG_TIMESTAMP_SOURCE_SYSTEM": true, "LWIP_ENABLE": true, "LWIP_LOCAL_HOSTNAME": true, "LWIP_NETIF_API": true, "LWIP_TCPIP_TASK_PRIO": true, "LWIP_TCPIP_CORE_LOCKING": true, "LWIP_TCPIP_CORE_LOCKING_INPUT": false, "LWIP_CHECK_THREAD_SAFETY": true, "LWIP_DNS_SUPPORT_MDNS_QUERIES": true, "LWIP_L2_TO_L3_COPY": true, "LWIP_IRAM_OPTIMIZATION": true, "LWIP_EXTRA_IRAM_OPTIMIZATION": true, "LWIP_TIMERS_ONDEMAND": true, "LWIP_ND6": true, "LWIP_FORCE_ROUTER_FORWARDING": true, "LWIP_MAX_SOCKETS": true, "LWIP_USE_ONLY_LWIP_SELECT": true, "LWIP_SO_LINGER": true, "LWIP_SO_REUSE": true, "LWIP_SO_REUSE_RXTOALL": true, "LWIP_SO_RCVBUF": true, "LWIP_NETBUF_RECVINFO": true, "LWIP_IP_DEFAULT_TTL": true, "LWIP_IP4_FRAG": true, "LWIP_IP6_FRAG": true, "LWIP_IP4_REASSEMBLY": true, "LWIP_IP6_REASSEMBLY": true, "LWIP_IP_REASS_MAX_PBUFS": true, "LWIP_IP_FORWARD": true, "LWIP_IPV4_NAPT": false, "LWIP_IPV4_NAPT_PORTMAP": false, "LWIP_STATS": true, "LWIP_ESP_GRATUITOUS_ARP": true, "LWIP_GARP_TMR_INTERVAL": true, "LWIP_ESP_MLDV6_REPORT": true, "LWIP_MLDV6_TMR_INTERVAL": true, "LWIP_TCPIP_RECVMBOX_SIZE": true, "LWIP_DHCP_DOES_ARP_CHECK": true, "LWIP_DHCP_DISABLE_CLIENT_ID": true, "LWIP_DHCP_DISABLE_VENDOR_CLASS_ID": true, "LWIP_DHCP_RESTORE_LAST_IP": true, "LWIP_DHCP_OPTIONS_LEN": true, "LWIP_NUM_NETIF_CLIENT_DATA": true, "LWIP_DHCP_COARSE_TIMER_SECS": true, "LWIP_DHCPS": true, "LWIP_DHCPS_LEASE_UNIT": true, "LWIP_DHCPS_MAX_STATION_NUM": true, "LWIP_DHCPS_STATIC_ENTRIES": true, "LWIP_AUTOIP": true, "LWIP_AUTOIP_TRIES": false, "LWIP_AUTOIP_MAX_CONFLICTS": false, "LWIP_AUTOIP_RATE_LIMIT_INTERVAL": false, "LWIP_IPV4": true, "LWIP_IPV6": true, "LWIP_IPV6_AUTOCONFIG": true, "LWIP_IPV6_NUM_ADDRESSES": true, "LWIP_IPV6_FORWARD": true, "LWIP_IPV6_RDNSS_MAX_DNS_SERVERS": false, "LWIP_IPV6_DHCP6": false, "LWIP_NETIF_STATUS_CALLBACK": true, "LWIP_NETIF_LOOPBACK": true, "LWIP_LOOPBACK_MAX_PBUFS": true, "LWIP_MAX_ACTIVE_TCP": true, "LWIP_MAX_LISTENING_TCP": true, "LWIP_TCP_HIGH_SPEED_RETRANSMISSION": true, "LWIP_TCP_MAXRTX": true, "LWIP_TCP_SYNMAXRTX": true, "LWIP_TCP_MSS": true, "LWIP_TCP_TMR_INTERVAL": true, "LWIP_TCP_MSL": true, "LWIP_TCP_FIN_WAIT_TIMEOUT": true, "LWIP_TCP_SND_BUF_DEFAULT": true, "LWIP_TCP_WND_DEFAULT": true, "LWIP_TCP_RECVMBOX_SIZE": true, "LWIP_TCP_ACCEPTMBOX_SIZE": true, "LWIP_TCP_QUEUE_OOSEQ": true, "LWIP_TCP_OOSEQ_TIMEOUT": true, "LWIP_TCP_OOSEQ_MAX_PBUFS": true, "LWIP_TCP_SACK_OUT": true, "component-config-lwip-tcp-pre-allocate-transmit-pbuf-size": true, "LWIP_TCP_OVERSIZE_MSS": true, "LWIP_TCP_OVERSIZE_QUARTER_MSS": true, "LWIP_TCP_OVERSIZE_DISABLE": true, "LWIP_WND_SCALE": false, "LWIP_TCP_RCV_SCALE": false, "LWIP_TCP_RTO_TIME": true, "LWIP_MAX_UDP_PCBS": true, "LWIP_UDP_RECVMBOX_SIZE": true, "LWIP_CHECKSUM_CHECK_IP": true, "LWIP_CHECKSUM_CHECK_UDP": true, "LWIP_CHECKSUM_CHECK_ICMP": true, "LWIP_TCPIP_TASK_STACK_SIZE": true, "component-config-lwip-tcp-ip-task-affinity": true, "LWIP_TCPIP_TASK_AFFINITY_NO_AFFINITY": true, "LWIP_TCPIP_TASK_AFFINITY_CPU0": true, "LWIP_TCPIP_TASK_AFFINITY_CPU1": false, "LWIP_TCPIP_TASK_AFFINITY": false, "LWIP_IPV6_ND6_NUM_PREFIXES": true, "LWIP_IPV6_ND6_NUM_ROUTERS": true, "LWIP_IPV6_ND6_NUM_DESTINATIONS": true, "LWIP_PPP_SUPPORT": true, "LWIP_PPP_ENABLE_IPV6": false, "LWIP_IPV6_MEMP_NUM_ND6_QUEUE": true, "LWIP_IPV6_ND6_NUM_NEIGHBORS": true, "LWIP_PPP_NOTIFY_PHASE_SUPPORT": false, "LWIP_PPP_PAP_SUPPORT": false, "LWIP_PPP_CHAP_SUPPORT": false, "LWIP_PPP_MSCHAP_SUPPORT": false, "LWIP_PPP_MPPE_SUPPORT": false, "LWIP_PPP_SERVER_SUPPORT": false, "LWIP_PPP_VJ_HEADER_COMPRESSION": false, "LWIP_ENABLE_LCP_ECHO": false, "LWIP_LCP_ECHOINTERVAL": false, "LWIP_LCP_MAXECHOFAILS": false, "LWIP_PPP_DEBUG_ON": false, "LWIP_USE_EXTERNAL_MBEDTLS": false, "LWIP_SLIP_SUPPORT": true, "LWIP_SLIP_DEBUG_ON": false, "LWIP_ICMP": true, "LWIP_MULTICAST_PING": true, "LWIP_BROADCAST_PING": true, "LWIP_MAX_RAW_PCBS": true, "LWIP_SNTP_MAX_SERVERS": true, "LWIP_DHCP_GET_NTP_SRV": true, "LWIP_DHCP_MAX_NTP_SERVERS": false, "LWIP_SNTP_UPDATE_DELAY": true, "LWIP_SNTP_STARTUP_DELAY": true, "LWIP_SNTP_MAXIMUM_STARTUP_DELAY": true, "LWIP_DNS_MAX_HOST_IP": true, "LWIP_DNS_MAX_SERVERS": true, "LWIP_FALLBACK_DNS_SERVER_SUPPORT": true, "LWIP_FALLBACK_DNS_SERVER_ADDRESS": false, "LWIP_DNS_SETSERVER_WITH_NETIF": true, "LWIP_BRIDGEIF_MAX_PORTS": true, "LWIP_ESP_LWIP_ASSERT": true, "component-config-lwip-hooks-tcp-isn-hook": true, "LWIP_HOOK_TCP_ISN_NONE": true, "LWIP_HOOK_TCP_ISN_DEFAULT": true, "LWIP_HOOK_TCP_ISN_CUSTOM": true, "component-config-lwip-hooks-ipv6-route-hook": true, "LWIP_HOOK_IP6_ROUTE_NONE": true, "LWIP_HOOK_IP6_ROUTE_DEFAULT": true, "LWIP_HOOK_IP6_ROUTE_CUSTOM": true, "component-config-lwip-hooks-ipv6-get-gateway-hook": true, "LWIP_HOOK_ND6_GET_GW_NONE": true, "LWIP_HOOK_ND6_GET_GW_DEFAULT": true, "LWIP_HOOK_ND6_GET_GW_CUSTOM": true, "component-config-lwip-hooks-ipv6-source-address-selection-hook": true, "LWIP_HOOK_IP6_SELECT_SRC_ADDR_NONE": true, "LWIP_HOOK_IP6_SELECT_SRC_ADDR_DEFAULT": true, "LWIP_HOOK_IP6_SELECT_SRC_ADDR_CUSTOM": true, "component-config-lwip-hooks-netconn-external-resolve-hook": true, "LWIP_HOOK_NETCONN_EXT_RESOLVE_NONE": true, "LWIP_HOOK_NETCONN_EXT_RESOLVE_DEFAULT": true, "LWIP_HOOK_NETCONN_EXT_RESOLVE_CUSTOM": true, "LWIP_HOOK_DNS_EXTERNAL_RESOLVE_SELECT_CUSTOM": false, "component-config-lwip-hooks-dns-external-resolve-hook": true, "LWIP_HOOK_DNS_EXT_RESOLVE_NONE": true, "LWIP_HOOK_DNS_EXT_RESOLVE_CUSTOM": true, "component-config-lwip-hooks-ipv6-packet-input": true, "LWIP_HOOK_IP6_INPUT_NONE": true, "LWIP_HOOK_IP6_INPUT_DEFAULT": true, "LWIP_HOOK_IP6_INPUT_CUSTOM": true, "LWIP_DEBUG": true, "LWIP_DEBUG_ESP_LOG": false, "LWIP_NETIF_DEBUG": false, "LWIP_PBUF_DEBUG": false, "LWIP_ETHARP_DEBUG": false, "LWIP_API_LIB_DEBUG": false, "LWIP_SOCKETS_DEBUG": false, "LWIP_IP_DEBUG": false, "LWIP_ICMP_DEBUG": false, "LWIP_DHCP_STATE_DEBUG": false, "LWIP_DHCP_DEBUG": false, "LWIP_IP6_DEBUG": false, "LWIP_ICMP6_DEBUG": false, "LWIP_TCP_DEBUG": false, "LWIP_UDP_DEBUG": false, "LWIP_SNTP_DEBUG": false, "LWIP_DNS_DEBUG": false, "LWIP_NAPT_DEBUG": false, "LWIP_BRIDGEIF_DEBUG": false, "LWIP_BRIDGEIF_FDB_DEBUG": false, "LWIP_BRIDGEIF_FW_DEBUG": false, "component-config-mbedtls-memory-allocation-strategy": true, "MBEDTLS_INTERNAL_MEM_ALLOC": true, "MBEDTLS_EXTERNAL_MEM_ALLOC": false, "MBEDTLS_DEFAULT_MEM_ALLOC": true, "MBEDTLS_CUSTOM_MEM_ALLOC": true, "MBEDTLS_IRAM_8BIT_MEM_ALLOC": false, "MBEDTLS_SSL_MAX_CONTENT_LEN": false, "MBEDTLS_ASYMMETRIC_CONTENT_LEN": true, "MBEDTLS_SSL_IN_CONTENT_LEN": true, "MBEDTLS_SSL_OUT_CONTENT_LEN": true, "MBEDTLS_DYNAMIC_BUFFER": true, "MBEDTLS_DYNAMIC_FREE_CONFIG_DATA": false, "MBEDTLS_DYNAMIC_FREE_CA_CERT": false, "MBEDTLS_DEBUG": true, "component-config-mbedtls-enable-mbedtls-debugging-set-mbedtls-debugging-level": false, "MBEDTLS_DEBUG_LEVEL_WARN": false, "MBEDTLS_DEBUG_LEVEL_INFO": false, "MBEDTLS_DEBUG_LEVEL_DEBUG": false, "MBEDTLS_DEBUG_LEVEL_VERBOSE": false, "MBEDTLS_DEBUG_LEVEL": false, "MBEDTLS_SSL_PROTO_TLS1_3": true, "MBEDTLS_SSL_TLS1_3_COMPATIBILITY_MODE": false, "MBEDTLS_SSL_TLS1_3_KEXM_PSK": false, "MBEDTLS_SSL_TLS1_3_KEXM_EPHEMERAL": false, "MBEDTLS_SSL_TLS1_3_KEXM_PSK_EPHEMERAL": false, "MBEDTLS_SSL_VARIABLE_BUFFER_LENGTH": true, "MBEDTLS_ECDH_LEGACY_CONTEXT": false, "MBEDTLS_X509_TRUSTED_CERT_CALLBACK": true, "MBEDTLS_SSL_CONTEXT_SERIALIZATION": true, "MBEDTLS_SSL_KEEP_PEER_CERTIFICATE": true, "MBEDTLS_PKCS7_C": true, "MBEDTLS_SSL_CID_PADDING_GRANULARITY": false, "MBEDTLS_SSL_DTLS_CONNECTION_ID": false, "MBEDTLS_SSL_CID_IN_LEN_MAX": false, "MBEDTLS_SSL_CID_OUT_LEN_MAX": false, "MBEDTLS_SSL_DTLS_SRTP": false, "MBEDTLS_CERTIFICATE_BUNDLE": true, "component-config-mbedtls-certificate-bundle-enable-trusted-root-certificate-bundle-default-certificate-bundle-options": true, "MBEDTLS_CERTIFICATE_BUNDLE_DEFAULT_FULL": true, "MBEDTLS_CERTIFICATE_BUNDLE_DEFAULT_CMN": true, "MBEDTLS_CERTIFICATE_BUNDLE_DEFAULT_NONE": true, "MBEDTLS_CUSTOM_CERTIFICATE_BUNDLE": true, "MBEDTLS_CUSTOM_CERTIFICATE_BUNDLE_PATH": false, "MBEDTLS_CERTIFICATE_BUNDLE_DEPRECATED_LIST": true, "MBEDTLS_CERTIFICATE_BUNDLE_MAX_CERTS": true, "MBEDTLS_ECP_RESTARTABLE": true, "MBEDTLS_CMAC_C": true, "MBEDTLS_HARDWARE_AES": true, "MBEDTLS_AES_USE_INTERRUPT": true, "MBEDTLS_AES_INTERRUPT_LEVEL": true, "MBEDTLS_AES_USE_PSEUDO_ROUND_FUNC": false, "component-config-mbedtls-enable-aes-hardware-s-pseudo-round-function-strength-of-the-pseudo-rounds-function": false, "MBEDTLS_AES_USE_PSEUDO_ROUND_FUNC_STRENGTH_LOW": false, "MBEDTLS_AES_USE_PSEUDO_ROUND_FUNC_STRENGTH_MEDIUM": false, "MBEDTLS_AES_USE_PSEUDO_ROUND_FUNC_STRENGTH_HIGH": false, "MBEDTLS_AES_USE_PSEUDO_ROUND_FUNC_STRENGTH": false, "MBEDTLS_HARDWARE_GCM": false, "MBEDTLS_GCM_SUPPORT_NON_AES_CIPHER": true, "MBEDTLS_HARDWARE_MPI": true, "MBEDTLS_LARGE_KEY_SOFTWARE_MPI": true, "MBEDTLS_MPI_USE_INTERRUPT": true, "MBEDTLS_MPI_INTERRUPT_LEVEL": true, "MBEDTLS_HARDWARE_SHA": true, "MBEDTLS_HARDWARE_ECC": false, "MBEDTLS_ECC_OTHER_CURVES_SOFT_FALLBACK": false, "MBEDTLS_ROM_MD5": true, "MBEDTLS_HARDWARE_ECDSA_SIGN": false, "MBEDTLS_HARDWARE_ECDSA_SIGN_MASKING_CM": false, "MBEDTLS_HARDWARE_ECDSA_SIGN_CONSTANT_TIME_CM": false, "MBEDTLS_HARDWARE_ECDSA_VERIFY": false, "MBEDTLS_ATCA_HW_ECDSA_SIGN": true, "MBEDTLS_ATCA_HW_ECDSA_VERIFY": true, "MBEDTLS_HAVE_TIME": true, "MBEDTLS_PLATFORM_TIME_ALT": true, "MBEDTLS_HAVE_TIME_DATE": true, "MBEDTLS_ECDSA_DETERMINISTIC": true, "MBEDTLS_SHA512_C": true, "MBEDTLS_SHA3_C": true, "component-config-mbedtls-tls-protocol-role": true, "MBEDTLS_TLS_SERVER_AND_CLIENT": true, "MBEDTLS_TLS_SERVER_ONLY": true, "MBEDTLS_TLS_CLIENT_ONLY": true, "MBEDTLS_TLS_DISABLED": true, "MBEDTLS_TLS_SERVER": false, "MBEDTLS_TLS_CLIENT": false, "MBEDTLS_TLS_ENABLED": false, "MBEDTLS_PSK_MODES": true, "MBEDTLS_KEY_EXCHANGE_PSK": false, "MBEDTLS_KEY_EXCHANGE_DHE_PSK": false, "MBEDTLS_KEY_EXCHANGE_ECDHE_PSK": false, "MBEDTLS_KEY_EXCHANGE_RSA_PSK": false, "MBEDTLS_KEY_EXCHANGE_RSA": true, "MBEDTLS_KEY_EXCHANGE_DHE_RSA": false, "MBEDTLS_KEY_EXCHANGE_ELLIPTIC_CURVE": true, "MBEDTLS_KEY_EXCHANGE_ECDHE_RSA": true, "MBEDTLS_KEY_EXCHANGE_ECDHE_ECDSA": true, "MBEDTLS_KEY_EXCHANGE_ECDH_ECDSA": true, "MBEDTLS_KEY_EXCHANGE_ECDH_RSA": true, "MBEDTLS_KEY_EXCHANGE_ECJPAKE": false, "MBEDTLS_SSL_RENEGOTIATION": true, "MBEDTLS_SSL_PROTO_TLS1_2": true, "MBEDTLS_SSL_PROTO_GMTSSL1_1": true, "MBEDTLS_SSL_PROTO_DTLS": true, "MBEDTLS_SSL_ALPN": true, "MBEDTLS_CLIENT_SSL_SESSION_TICKETS": true, "MBEDTLS_SERVER_SSL_SESSION_TICKETS": true, "MBEDTLS_AES_C": true, "MBEDTLS_CAMELLIA_C": true, "MBEDTLS_DES_C": true, "MBEDTLS_BLOWFISH_C": true, "MBEDTLS_XTEA_C": true, "MBEDTLS_CCM_C": true, "MBEDTLS_GCM_C": true, "MBEDTLS_NIST_KW_C": true, "MBEDTLS_RIPEMD160_C": true, "MBEDTLS_PEM_PARSE_C": true, "MBEDTLS_PEM_WRITE_C": true, "MBEDTLS_X509_CRL_PARSE_C": true, "MBEDTLS_X509_CSR_PARSE_C": true, "MBEDTLS_ECP_C": true, "MBEDTLS_DHM_C": true, "MBEDTLS_ECDH_C": true, "MBEDTLS_ECDSA_C": true, "MBEDTLS_ECJPAKE_C": true, "MBEDTLS_ECP_DP_SECP192R1_ENABLED": true, "MBEDTLS_ECP_DP_SECP224R1_ENABLED": true, "MBEDTLS_ECP_DP_SECP256R1_ENABLED": true, "MBEDTLS_ECP_DP_SECP384R1_ENABLED": true, "MBEDTLS_ECP_DP_SECP521R1_ENABLED": true, "MBEDTLS_ECP_DP_SECP192K1_ENABLED": true, "MBEDTLS_ECP_DP_SECP224K1_ENABLED": true, "MBEDTLS_ECP_DP_SECP256K1_ENABLED": true, "MBEDTLS_ECP_DP_BP256R1_ENABLED": true, "MBEDTLS_ECP_DP_BP384R1_ENABLED": true, "MBEDTLS_ECP_DP_BP512R1_ENABLED": true, "MBEDTLS_ECP_DP_CURVE25519_ENABLED": true, "MBEDTLS_ECP_NIST_OPTIM": true, "MBEDTLS_ECP_FIXED_POINT_OPTIM": true, "MBEDTLS_POLY1305_C": true, "MBEDTLS_CHACHA20_C": true, "MBEDTLS_CHACHAPOLY_C": false, "MBEDTLS_HKDF_C": true, "MBEDTLS_THREADING_C": true, "MBEDTLS_THREADING_ALT": false, "MBEDTLS_THREADING_PTHREAD": false, "MBEDTLS_ERROR_STRINGS": true, "MBEDTLS_USE_CRYPTO_ROM_IMPL": false, "MBEDTLS_FS_IO": true, "MQTT_PROTOCOL_311": true, "MQTT_PROTOCOL_5": true, "MQTT_TRANSPORT_SSL": true, "MQTT_TRANSPORT_WEBSOCKET": true, "MQTT_TRANSPORT_WEBSOCKET_SECURE": true, "MQTT_MSG_ID_INCREMENTAL": true, "MQTT_SKIP_PUBLISH_IF_DISCONNECTED": true, "MQTT_REPORT_DELETED_MESSAGES": true, "MQTT_USE_CUSTOM_CONFIG": true, "MQTT_TCP_DEFAULT_PORT": false, "MQTT_SSL_DEFAULT_PORT": false, "MQTT_WS_DEFAULT_PORT": false, "MQTT_WSS_DEFAULT_PORT": false, "MQTT_BUFFER_SIZE": false, "MQTT_TASK_STACK_SIZE": false, "MQTT_DISABLE_API_LOCKS": false, "MQTT_TASK_PRIORITY": false, "MQTT_POLL_READ_TIMEOUT_MS": false, "MQTT_EVENT_QUEUE_SIZE": false, "MQTT_TASK_CORE_SELECTION_ENABLED": true, "component-config-esp-mqtt-configurations-enable-mqtt-task-core-selection-core-to-use-": false, "MQTT_USE_CORE_0": false, "MQTT_USE_CORE_1": false, "MQTT_OUTBOX_DATA_ON_EXTERNAL_MEMORY": false, "MQTT_CUSTOM_OUTBOX": true, "MQTT_OUTBOX_EXPIRED_TIMEOUT_MS": false, "component-config-newlib-line-ending-for-uart-output": true, "NEWLIB_STDOUT_LINE_ENDING_CRLF": true, "NEWLIB_STDOUT_LINE_ENDING_LF": true, "NEWLIB_STDOUT_LINE_ENDING_CR": true, "component-config-newlib-line-ending-for-uart-input": true, "NEWLIB_STDIN_LINE_ENDING_CRLF": true, "NEWLIB_STDIN_LINE_ENDING_LF": true, "NEWLIB_STDIN_LINE_ENDING_CR": true, "NEWLIB_NANO_FORMAT": true, "component-config-newlib-timers-used-for-gettimeofday-function": true, "NEWLIB_TIME_SYSCALL_USE_RTC_HRT": true, "NEWLIB_TIME_SYSCALL_USE_RTC": true, "NEWLIB_TIME_SYSCALL_USE_HRT": true, "NEWLIB_TIME_SYSCALL_USE_NONE": true, "STDATOMIC_S32C1I_SPIRAM_WORKAROUND": false, "NVS_ENCRYPTION": true, "NVS_COMPATIBLE_PRE_V4_3_ENCRYPTION_FLAG": false, "NVS_ASSERT_ERROR_CHECK": true, "NVS_LEGACY_DUP_KEYS_COMPATIBILITY": true, "component-config-nvs-security-provider-nvs-encryption-key-protection-scheme": false, "NVS_SEC_KEY_PROTECT_USING_FLASH_ENC": false, "NVS_SEC_KEY_PROTECT_USING_HMAC": false, "NVS_SEC_HMAC_EFUSE_KEY_ID": false, "OPENTHREAD_ENABLED": true, "OPENTHREAD_PACKAGE_NAME": false, "OPENTHREAD_PLATFORM_INFO": false, "component-config-openthread-openthread-thread-console-openthread-console-type": false, "OPENTHREAD_CONSOLE_TYPE_UART": false, "OPENTHREAD_CONSOLE_TYPE_USB_SERIAL_JTAG": false, "OPENTHREAD_CLI": false, "OPENTHREAD_NETWORK_NAME": false, "OPENTHREAD_MESH_LOCAL_PREFIX": false, "OPENTHREAD_NETWORK_CHANNEL": false, "OPENTHREAD_NETWORK_PANID": false, "OPENTHREAD_NETWORK_EXTPANID": false, "OPENTHREAD_NETWORK_MASTERKEY": false, "OPENTHREAD_NETWORK_PSKC": false, "component-config-openthread-openthread-thread-core-features-thread-device-type": false, "OPENTHREAD_FTD": false, "OPENTHREAD_MTD": false, "OPENTHREAD_RADIO": false, "OPENTHREAD_RADIO_TREL": false, "OPENTHREAD_TREL_PORT": false, "component-config-openthread-openthread-thread-core-features-thread-15-4-radio-link-config-the-thread-radio-type-with-15-4-link": false, "OPENTHREAD_RADIO_NATIVE": false, "OPENTHREAD_RADIO_SPINEL_UART": false, "OPENTHREAD_RADIO_SPINEL_SPI": false, "OPENTHREAD_RADIO_154_NONE": false, "component-config-openthread-openthread-thread-core-features-thread-radio-co-processor-feature-the-rcp-transport-type": false, "OPENTHREAD_RCP_UART": false, "OPENTHREAD_RCP_SPI": false, "OPENTHREAD_NCP_VENDOR_HOOK": false, "OPENTHREAD_BORDER_ROUTER": false, "OPENTHREAD_COMMISSIONER": false, "OPENTHREAD_COMM_MAX_JOINER_ENTRIES": false, "OPENTHREAD_JOINER": false, "OPENTHREAD_SRP_CLIENT": false, "OPENTHREAD_SRP_CLIENT_MAX_SERVICES": false, "OPENTHREAD_DNS_CLIENT": false, "OPENTHREAD_DNS64_CLIENT": false, "OPENTHREAD_DNS_SERVER_ADDR": false, "OPENTHREAD_LINK_METRICS": false, "OPENTHREAD_MACFILTER_ENABLE": false, "OPENTHREAD_CSL_ENABLE": false, "OPENTHREAD_CSL_ACCURACY": false, "OPENTHREAD_CSL_UNCERTAIN": false, "OPENTHREAD_CSL_DEBUG_ENABLE": false, "OPENTHREAD_TIME_SYNC": false, "OPENTHREAD_RADIO_STATS_ENABLE": false, "OPENTHREAD_RX_ON_WHEN_IDLE": false, "OPENTHREAD_DIAG": false, "OPENTHREAD_PLATFORM_MALLOC_CAP_SPIRAM": false, "OPENTHREAD_PLATFORM_MSGPOOL_MANAGEMENT": false, "OPENTHREAD_ADDRESS_QUERY_TIMEOUT": false, "OPENTHREAD_ADDRESS_QUERY_RETRY_DELAY": false, "OPENTHREAD_ADDRESS_QUERY_MAX_RETRY_DELAY": false, "OPENTHREAD_PREFERRED_CHANNEL_MASK": false, "OPENTHREAD_SUPPORTED_CHANNEL_MASK": false, "OPENTHREAD_NUM_MESSAGE_BUFFERS": false, "OPENTHREAD_XTAL_ACCURACY": false, "OPENTHREAD_MLE_MAX_CHILDREN": false, "OPENTHREAD_TMF_ADDR_CACHE_ENTRIES": false, "OPENTHREAD_UART_BUFFER_SIZE": false, "OPENTHREAD_MAC_MAX_CSMA_BACKOFFS_DIRECT": false, "OPENTHREAD_LOG_LEVEL_DYNAMIC": false, "component-config-openthread-openthread-thread-log-openthread-log-verbosity": false, "OPENTHREAD_LOG_LEVEL_NONE": false, "OPENTHREAD_LOG_LEVEL_CRIT": false, "OPENTHREAD_LOG_LEVEL_WARN": false, "OPENTHREAD_LOG_LEVEL_NOTE": false, "OPENTHREAD_LOG_LEVEL_INFO": false, "OPENTHREAD_LOG_LEVEL_DEBG": false, "OPENTHREAD_LOG_LEVEL": false, "OPENTHREAD_HEADER_CUSTOM": false, "OPENTHREAD_CUSTOM_HEADER_PATH": false, "OPENTHREAD_CUSTOM_HEADER_FILE_NAME": false, "OPENTHREAD_SPINEL_ONLY": true, "OPENTHREAD_SPINEL_RX_FRAME_BUFFER_SIZE": false, "OPENTHREAD_SPINEL_MAC_MAX_CSMA_BACKOFFS_DIRECT": false, "ESP_PROTOCOMM_SUPPORT_SECURITY_VERSION_0": true, "ESP_PROTOCOMM_SUPPORT_SECURITY_VERSION_1": true, "ESP_PROTOCOMM_SUPPORT_SECURITY_VERSION_2": true, "ESP_PROTOCOMM_SUPPORT_SECURITY_PATCH_VERSION": false, "ESP_PROTOCOMM_KEEP_BLE_ON_AFTER_BLE_STOP": false, "ESP_PROTOCOMM_DISCONNECT_AFTER_BLE_STOP": false, "PTHREAD_TASK_PRIO_DEFAULT": true, "PTHREAD_TASK_STACK_SIZE_DEFAULT": true, "PTHREAD_STACK_MIN": true, "component-config-pthreads-default-pthread-core-affinity": false, "PTHREAD_DEFAULT_CORE_NO_AFFINITY": false, "PTHREAD_DEFAULT_CORE_0": false, "PTHREAD_DEFAULT_CORE_1": false, "PTHREAD_TASK_CORE_DEFAULT": false, "PTHREAD_TASK_NAME_DEFAULT": true, "MMU_PAGE_SIZE_16KB": false, "MMU_PAGE_SIZE_32KB": false, "MMU_PAGE_SIZE_64KB": false, "MMU_PAGE_MODE": false, "MMU_PAGE_SIZE": false, "SPI_FLASH_BROWNOUT_RESET_XMC": true, "SPI_FLASH_BROWNOUT_RESET": false, "SPI_FLASH_UNDER_HIGH_FREQ": false, "component-config-main-flash-configuration-optional-and-experimental-features-read-docs-first--high-performance-mode-read-docs-first-80mhz-": false, "SPI_FLASH_HPM_ENA": false, "SPI_FLASH_HPM_AUTO": false, "SPI_FLASH_HPM_DIS": false, "SPI_FLASH_HPM_ON": false, "component-config-main-flash-configuration-optional-and-experimental-features-read-docs-first--support-hpm-using-dc-read-docs-first-": false, "SPI_FLASH_HPM_DC_AUTO": false, "SPI_FLASH_HPM_DC_DISABLE": false, "SPI_FLASH_HPM_DC_ON": false, "SPI_FLASH_SUSPEND_QVL_SUPPORTED": false, "SPI_FLASH_AUTO_SUSPEND": true, "SPI_FLASH_SUSPEND_TSUS_VAL_US": true, "SPI_FLASH_FORCE_ENABLE_XMC_C_SUSPEND": true, "SPI_FLASH_SOFTWARE_RESUME": false, "SPI_FLASH_DISABLE_SCHEDULER_IN_SUSPEND": false, "SPI_FLASH_AUTO_CHECK_SUSPEND_STATUS": false, "SPI_FLASH_VERIFY_WRITE": true, "SPI_FLASH_LOG_FAILED_WRITE": false, "SPI_FLASH_WARN_SETTING_ZERO_TO_ONE": false, "SPI_FLASH_ENABLE_COUNTERS": true, "SPI_FLASH_ROM_DRIVER_PATCH": true, "SPI_FLASH_ROM_IMPL": true, "component-config-spi-flash-driver-writing-to-dangerous-flash-regions": true, "SPI_FLASH_DANGEROUS_WRITE_ABORTS": true, "SPI_FLASH_DANGEROUS_WRITE_FAILS": true, "SPI_FLASH_DANGEROUS_WRITE_ALLOWED": true, "SPI_FLASH_SHARE_SPI1_BUS": false, "SPI_FLASH_BYPASS_BLOCK_ERASE": true, "SPI_FLASH_YIELD_DURING_ERASE": true, "SPI_FLASH_ERASE_YIELD_DURATION_MS": true, "SPI_FLASH_ERASE_YIELD_TICKS": true, "SPI_FLASH_WRITE_CHUNK_SIZE": true, "SPI_FLASH_SIZE_OVERRIDE": true, "SPI_FLASH_CHECK_ERASE_TIMEOUT_DISABLED": true, "SPI_FLASH_OVERRIDE_CHIP_DRIVER_LIST": true, "SPI_FLASH_VENDOR_XMC_SUPPORTED": false, "SPI_FLASH_VENDOR_GD_SUPPORTED": false, "SPI_FLASH_VENDOR_ISSI_SUPPORTED": false, "SPI_FLASH_VENDOR_MXIC_SUPPORTED": false, "SPI_FLASH_VENDOR_WINBOND_SUPPORTED": false, "SPI_FLASH_VENDOR_BOYA_SUPPORTED": false, "SPI_FLASH_VENDOR_TH_SUPPORTED": false, "SPI_FLASH_SUPPORT_ISSI_CHIP": true, "SPI_FLASH_SUPPORT_MXIC_CHIP": true, "SPI_FLASH_SUPPORT_GD_CHIP": true, "SPI_FLASH_SUPPORT_WINBOND_CHIP": true, "SPI_FLASH_SUPPORT_BOYA_CHIP": true, "SPI_FLASH_SUPPORT_TH_CHIP": true, "SPI_FLASH_SUPPORT_MXIC_OPI_CHIP": false, "SPI_FLASH_ENABLE_ENCRYPTED_READ_WRITE": true, "SPIFFS_MAX_PARTITIONS": true, "SPIFFS_CACHE": true, "SPIFFS_CACHE_WR": true, "SPIFFS_CACHE_STATS": true, "SPIFFS_PAGE_CHECK": true, "SPIFFS_GC_MAX_RUNS": true, "SPIFFS_GC_STATS": true, "SPIFFS_PAGE_SIZE": true, "SPIFFS_OBJ_NAME_LEN": true, "SPIFFS_FOLLOW_SYMLINKS": true, "SPIFFS_USE_MAGIC": true, "SPIFFS_USE_MAGIC_LENGTH": true, "SPIFFS_META_LENGTH": true, "SPIFFS_USE_MTIME": true, "SPIFFS_MTIME_WIDE_64_BITS": false, "SPIFFS_DBG": true, "SPIFFS_API_DBG": true, "SPIFFS_GC_DBG": true, "SPIFFS_CACHE_DBG": true, "SPIFFS_CHECK_DBG": true, "SPIFFS_TEST_VISUALISATION": true, "WS_TRANSPORT": true, "WS_BUFFER_SIZE": true, "WS_DYNAMIC_BUFFER": true, "ULP_COPROC_ENABLED": false, "component-config-ultra-low-power-ulp-co-processor-enable-ultra-low-power-ulp-co-processor-ulp-co-processor-type": false, "ULP_COPROC_TYPE_FSM": false, "ULP_COPROC_TYPE_RISCV": false, "ULP_COPROC_TYPE_LP_CORE": false, "ULP_COPROC_RESERVE_MEM": false, "ULP_RISCV_INTERRUPT_ENABLE": false, "ULP_RISCV_UART_BAUDRATE": false, "ULP_RISCV_I2C_RW_TIMEOUT": false, "ULP_SHARED_MEM": false, "ULP_ROM_PRINT_ENABLE": false, "ULP_PANIC_OUTPUT_ENABLE": false, "ULP_HP_UART_CONSOLE_PRINT": false, "UNITY_ENABLE_FLOAT": true, "UNITY_ENABLE_DOUBLE": true, "UNITY_ENABLE_64BIT": true, "UNITY_ENABLE_COLOR": true, "UNITY_ENABLE_IDF_TEST_RUNNER": true, "UNITY_ENABLE_FIXTURE": true, "UNITY_ENABLE_BACKTRACE_ON_FAIL": true, "USB_HOST_CONTROL_TRANSFER_MAX_SIZE": false, "component-config-usb-otg-hardware-fifo-size-biasing": false, "USB_HOST_HW_BUFFER_BIAS_BALANCED": false, "USB_HOST_HW_BUFFER_BIAS_IN": false, "USB_HOST_HW_BUFFER_BIAS_PERIODIC_OUT": false, "USB_HOST_DEBOUNCE_DELAY_MS": false, "USB_HOST_RESET_HOLD_MS": false, "USB_HOST_RESET_RECOVERY_MS": false, "USB_HOST_SET_ADDR_RECOVERY_MS": false, "USB_HOST_HUBS_SUPPORTED": false, "USB_HOST_HUB_MULTI_LEVEL": false, "USB_HOST_EXT_PORT_SUPPORT_LS": false, "USB_HOST_EXT_PORT_RESET_ATTEMPTS": false, "USB_HOST_EXT_PORT_RESET_RECOVERY_DELAY_MS": false, "USB_HOST_EXT_PORT_CUSTOM_POWER_ON_DELAY_ENABLE": false, "USB_HOST_EXT_PORT_CUSTOM_POWER_ON_DELAY_MS": false, "USB_HOST_ENABLE_ENUM_FILTER_CALLBACK": false, "USB_OTG_SUPPORTED": false, "VFS_SUPPORT_IO": true, "VFS_SUPPORT_DIR": true, "VFS_SUPPORT_SELECT": true, "VFS_SUPPRESS_SELECT_DEBUG_OUTPUT": true, "VFS_SELECT_IN_RAM": true, "VFS_SUPPORT_TERMIOS": true, "VFS_MAX_COUNT": true, "VFS_SEMIHOSTFS_MAX_MOUNT_POINTS": true, "component-config-wear-levelling-wear-levelling-library-sector-size": true, "WL_SECTOR_SIZE_512": true, "WL_SECTOR_SIZE_4096": true, "WL_SECTOR_SIZE": false, "component-config-wear-levelling-sector-store-mode": false, "WL_SECTOR_MODE_PERF": false, "WL_SECTOR_MODE_SAFE": false, "WL_SECTOR_MODE": false, "WIFI_PROV_SCAN_MAX_ENTRIES": true, "WIFI_PROV_AUTOSTOP_TIMEOUT": true, "WIFI_PROV_BLE_BONDING": true, "WIFI_PROV_BLE_SEC_CONN": false, "WIFI_PROV_BLE_FORCE_ENCRYPTION": true, "WIFI_PROV_BLE_NOTIFY": true, "WIFI_PROV_KEEP_BLE_ON_AFTER_PROV": true, "WIFI_PROV_DISCONNECT_AFTER_PROV": false, "component-config-wi-fi-provisioning-manager-wifi-provisioning-scan-method": true, "WIFI_PROV_STA_ALL_CHANNEL_SCAN": true, "WIFI_PROV_STA_FAST_SCAN": true, "IDF_EXPERIMENTAL_FEATURES": true, "component-config-wi-fi-provisioning-manager": true, "component-config-wear-levelling": true, "component-config-virtual-file-system-provide-basic-i-o-functions-host-file-system-i-o-semihosting-": true, "component-config-virtual-file-system": true, "component-config-usb-otg-hub-driver-configuration-support-hubs-downstream-port-configuration": false, "component-config-usb-otg-hub-driver-configuration-root-port-configuration": false, "component-config-usb-otg-hub-driver-configuration": false, "component-config-usb-otg": false, "component-config-unity-unit-testing-library": true, "component-config-ultra-low-power-ulp-co-processor-ulp-debugging-options": false, "component-config-ultra-low-power-ulp-co-processor-ulp-risc-v-settings": false, "component-config-ultra-low-power-ulp-co-processor": false, "component-config-tcp-transport-websocket": true, "component-config-tcp-transport": true, "component-config-spiffs-configuration-debug-configuration": true, "component-config-spiffs-configuration-spiffs-cache-configuration": true, "component-config-spiffs-configuration": true, "component-config-spi-flash-driver-auto-detect-flash-chips": true, "component-config-spi-flash-driver": true, "component-config-main-flash-configuration-optional-and-experimental-features-read-docs-first--features-here-require-specific-hardware-read-docs-first-": false, "component-config-main-flash-configuration-optional-and-experimental-features-read-docs-first-": true, "component-config-main-flash-configuration-spi-flash-behavior-when-brownout": true, "component-config-main-flash-configuration": true, "component-config-soc-settings-mmu-config": false, "component-config-soc-settings": false, "component-config-pthreads": true, "component-config-protocomm": true, "component-config-openthread-openthread-spinel": true, "component-config-openthread-openthread-thread-extensioned-features-use-a-header-file-defined-by-customer-openthread-custom-header-config": false, "component-config-openthread-openthread-thread-extensioned-features": false, "component-config-openthread-openthread-thread-log": false, "component-config-openthread-openthread-thread-core-features-openthread-stack-parameters-thread-address-query-config": false, "component-config-openthread-openthread-thread-core-features-openthread-stack-parameters": false, "component-config-openthread-openthread-thread-core-features-thread-memory-allocation": false, "component-config-openthread-openthread-thread-core-features-enable-csl-feature-csl-configurations": false, "component-config-openthread-openthread-thread-core-features-enable-dns64-client-dns64-client-configurations": false, "component-config-openthread-openthread-thread-core-features-enable-srp-client-srp-client-configurations": false, "component-config-openthread-openthread-thread-core-features-enable-commissioner-commissioner-configurations": false, "component-config-openthread-openthread-thread-core-features-thread-radio-co-processor-feature": false, "component-config-openthread-openthread-thread-core-features-thread-15-4-radio-link": false, "component-config-openthread-openthread-thread-core-features-thread-trel-radio-link": false, "component-config-openthread-openthread-thread-core-features-thread-operational-dataset": false, "component-config-openthread-openthread-thread-core-features": false, "component-config-openthread-openthread-thread-console": false, "component-config-openthread-openthread-thread-version-message": false, "component-config-openthread": true, "component-config-nvs-security-provider": false, "component-config-nvs": true, "component-config-newlib": true, "component-config-esp-mqtt-configurations": true, "component-config-mbedtls-certificates": true, "component-config-mbedtls-symmetric-ciphers": true, "component-config-mbedtls-tls-key-exchange-methods": true, "component-config-mbedtls-enable-ecdsa-signing-using-on-chip-ecdsa-peripheral-enable-software-countermeasure-for-ecdsa-signing-using-on-chip-ecdsa-peripheral": false, "component-config-mbedtls-certificate-bundle": true, "component-config-mbedtls-mbedtls-v3-x-related-dtls-based-configurations": false, "component-config-mbedtls-mbedtls-v3-x-related-support-tls-1-3-protocol-tls-1-3-related-configurations": false, "component-config-mbedtls-mbedtls-v3-x-related": true, "component-config-mbedtls": true, "component-config-lwip-hooks": true, "component-config-lwip-dns": true, "component-config-lwip-sntp": true, "component-config-lwip-lwip-raw-api": true, "component-config-lwip-icmp": true, "component-config-lwip-checksums": true, "component-config-lwip-udp": true, "component-config-lwip-tcp": true, "component-config-lwip-dhcp-server": true, "component-config-lwip": true, "component-config-log-output": true, "component-config-ieee-802-15-4": false, "component-config-heap-memory-debugging": true, "component-config-hardware-abstraction-layer-hal-and-low-level-ll-": true, "component-config-freertos-port": true, "component-config-freertos-kernel": true, "component-config-freertos": true, "component-config-fat-filesystem-support": true, "component-config-core-dump": true, "component-config-wi-fi-wps-configuration-options": true, "component-config-wi-fi-advanced-support-for-wi-fi-roaming-experimental--configure-roaming-app-scan-configuration": false, "component-config-wi-fi-advanced-support-for-wi-fi-roaming-experimental--configure-roaming-app-roaming-methods": false, "component-config-wi-fi-advanced-support-for-wi-fi-roaming-experimental--configure-roaming-app-roaming-triggers": false, "component-config-wi-fi-advanced-support-for-wi-fi-roaming-experimental--configure-roaming-app": false, "component-config-wi-fi": true, "component-config-esp-timer-high-resolution-timer-": true, "component-config-ipc-inter-processor-call-": true, "component-config-esp-system-settings-brownout-detector": true, "component-config-esp-system-settings-memory-protection": true, "component-config-esp-system-settings": true, "component-config-esp-ringbuf": true, "component-config-esp-psram": false, "component-config-power-management": true, "component-config-phy": true, "component-config-partition-api-configuration": false, "component-config-esp-netif-adapter": true, "component-config-lcd-and-touch-panel-lcd-peripheral-configuration": true, "component-config-lcd-and-touch-panel-lcd-touch-drivers-are-maintained-in-the-idf-component-registry": false, "component-config-lcd-and-touch-panel": true, "component-config-hardware-settings-crypto-dpa-protection": false, "component-config-hardware-settings-main-xtal-config": true, "component-config-hardware-settings-2d-dma-configurations": false, "component-config-hardware-settings-dw_gdma-configurations": false, "component-config-hardware-settings-gdma-configurations": true, "component-config-hardware-settings-etm-configuration": false, "component-config-hardware-settings-peripheral-control": true, "component-config-hardware-settings-rtc-clock-config": true, "component-config-hardware-settings-sleep-config": true, "component-config-hardware-settings-mac-config": true, "component-config-hardware-settings-chip-revision-maximum-supported-esp32-c3-efuse-block-revision-efuse-block-rev-v1-99-": false, "component-config-hardware-settings-chip-revision-maximum-supported-esp32-c3-revision-rev-v1-99-": false, "component-config-hardware-settings-chip-revision": true, "component-config-hardware-settings": true, "component-config-esp-https-server": true, "component-config-esp-https-ota": true, "component-config-http-server": true, "component-config-esp-http-client": true, "component-config-esp-hid": true, "component-config-gdb-stub": true, "component-config-event-loop-library": true, "component-config-ethernet": true, "component-config-esp-driver-usb-serial-jtag-configuration": true, "component-config-esp-driver-uart-configurations": true, "component-config-esp-driver-temperature-sensor-configurations": true, "component-config-esp-driver-touch-sensor-configurations": false, "component-config-esp-driver-spi-configurations": true, "component-config-esp-driver-sigma-delta-modulator-configurations": true, "component-config-esp-driver-rmt-configurations": true, "component-config-esp-driver-pcnt-configurations": false, "component-config-esp-driver-parallel-io-configurations": false, "component-config-esp-driver-mcpwm-configurations": false, "component-config-esp-driver-ledc-configurations": true, "component-config-esp-driver-jpeg-codec-configurations": false, "component-config-esp-driver-isp-configurations": false, "component-config-esp-driver-i2s-configurations": true, "component-config-esp-driver-i2c-configurations": true, "component-config-esp-driver-gptimer-configurations": true, "component-config-esp-driver-gpio-configurations": true, "component-config-esp-driver-dac-configurations": false, "component-config-esp-driver-camera-controller-configurations": false, "component-config-esp-driver-analog-comparator-configurations": false, "component-config-common-esp-related": true, "component-config-wireless-coexistence": true, "component-config-adc-and-adc-calibration-adc-calibration-configurations": false, "component-config-adc-and-adc-calibration": true, "component-config-esp-tls": true, "component-config-efuse-bit-manager": true, "component-config-driver-configurations-legacy-temperature-sensor-driver-configurations": true, "component-config-driver-configurations-legacy-sdm-driver-configurations": true, "component-config-driver-configurations-legacy-pcnt-driver-configurations": false, "component-config-driver-configurations-legacy-i2s-driver-configurations": true, "component-config-driver-configurations-legacy-rmt-driver-configurations": true, "component-config-driver-configurations-legacy-timer-group-driver-configurations": true, "component-config-driver-configurations-legacy-mcpwm-driver-configurations": false, "component-config-driver-configurations-legacy-dac-driver-configurations": false, "component-config-driver-configurations-legacy-adc-driver-configuration-legacy-adc-calibration-configuration": true, "component-config-driver-configurations-legacy-adc-driver-configuration": true, "component-config-driver-configurations-twai-configuration": true, "component-config-driver-configurations": true, "component-config-console-library": true, "component-config-esp-ble-mesh-support-ble-mesh-specific-test-option": false, "component-config-esp-ble-mesh-support-support-for-ble-mesh-client-server-models": false, "component-config-esp-ble-mesh-support-support-for-ble-mesh-foundation-models": false, "component-config-esp-ble-mesh-support-ble-mesh-net-buf-debug-log-level": false, "component-config-esp-ble-mesh-support-ble-mesh-stack-debug-log-level": false, "component-config-esp-ble-mesh-support-ble-mesh-and-ble-coexistence-support": false, "component-config-bluetooth-common-options": true, "component-config-bluetooth-controller-options-controller-debug-log-options-experimental-": false, "component-config-bluetooth-controller-options-ble-disconnect-when-instant-passed": true, "component-config-bluetooth-controller-options-modem-sleep-options": true, "component-config-bluetooth-controller-options": true, "component-config-bluetooth-nimble-options-host-controller-transport": false, "component-config-bluetooth-nimble-options-ble-services-device-information-service": false, "component-config-bluetooth-nimble-options-ble-services": false, "component-config-bluetooth-nimble-options-gap-service-gap-device-name-write-permissions": false, "component-config-bluetooth-nimble-options-gap-service-gap-appearance-write-permissions": false, "component-config-bluetooth-nimble-options-gap-service": false, "component-config-bluetooth-nimble-options-memory-settings": false, "component-config-bluetooth-nimble-options": false, "component-config-bluetooth-bluedroid-options-bt-debug-log-level": true, "component-config-bluetooth-bluedroid-options": true, "component-config-bluetooth": true, "component-config-application-level-tracing-freertos-systemview-tracing": false, "component-config-application-level-tracing": true, "component-config": true, "compiler-options": true, "partition-table": true, "serial-flasher-config": true, "boot-rom-behavior": true, "application-manager": true, "security-features-potentially-insecure-options": false, "security-features": true, "bootloader-config-serial-flash-configurations": true, "bootloader-config-bootloader-manager": true, "bootloader-config": true, "build-type": true}}
