<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ESP32 BLE Mill Monitor</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .content {
            padding: 30px;
        }
        .section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 25px;
            border-left: 5px solid #4CAF50;
        }
        .section h2 {
            margin-top: 0;
            color: #333;
            font-size: 1.5em;
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }
        .form-group input[type="text"], .form-group input[type="file"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
            box-sizing: border-box;
        }
        .form-group input[type="text"]:focus, .form-group input[type="file"]:focus {
            outline: none;
            border-color: #4CAF50;
        }
        .btn {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s;
            min-width: 120px;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
        }
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        .btn-danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
        }
        .btn-danger:hover {
            box-shadow: 0 5px 15px rgba(255, 107, 107, 0.4);
        }
        .btn-info {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
        }
        .btn-info:hover {
            box-shadow: 0 5px 15px rgba(23, 162, 184, 0.4);
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .info-display {
            background: #e3f2fd;
            border: 1px solid #90caf9;
            color: #0d47a1;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-family: monospace;
            font-size: 14px;
        }
        .flex-row {
            display: flex;
            gap: 15px;
            align-items: end;
        }
        .flex-row .form-group {
            flex: 1;
        }
        .flex-row .btn {
            margin-bottom: 0;
        }
        @media (max-width: 768px) {
            .flex-row {
                flex-direction: column;
            }
            .content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>ESP32 BLE Mill Monitor</h1>
            <p>File Server & Device Management</p>
        </div>

        <div class="content">
            <!-- Device ID Management Section -->
            <div class="section">
                <h2>🆔 Device ID Management</h2>
                <div class="info-display" id="currentIdDisplay">
                    Current Device ID: Loading...
                </div>
                <div class="flex-row">
                    <div class="form-group">
                        <label for="deviceId">Set New Device ID</label>
                        <input id="deviceId" type="text" placeholder="Enter device ID (1-255)" maxlength="3">
                    </div>
                    <button class="btn btn-info" onclick="setDeviceId()">Set ID</button>
                    <button class="btn" onclick="getCurrentId()">Refresh</button>
                </div>
            </div>

            <!-- File Upload Section -->
            <div class="section">
                <h2>📁 File Upload</h2>
                <div class="form-group">
                    <label for="newfile">Select File to Upload</label>
                    <input id="newfile" type="file" onchange="setpath()">
                </div>
                <div class="flex-row">
                    <div class="form-group">
                        <label for="filepath">File Path on Server</label>
                        <input id="filepath" type="text" placeholder="Enter file path">
                    </div>
                    <button id="upload" class="btn" onclick="upload()">Upload</button>
                </div>
            </div>

            <!-- Firmware Update Section -->
            <div class="section">
                <h2>🔄 Firmware Update (OTA)</h2>
                <div class="warning">
                    <strong>⚠️ Warning:</strong> Only upload valid ESP32 firmware (.bin) files! Incorrect firmware may brick your device.
                </div>
                <div class="form-group">
                    <label for="firmwarefile">Select Firmware File (.bin)</label>
                    <input id="firmwarefile" type="file" accept=".bin">
                </div>
                <button id="otaupload" class="btn btn-danger" onclick="uploadFirmware()">Update Firmware</button>
            </div>
        </div>
    </div>
    <script>
        // Load current device ID when page loads
        window.onload = function() {
            getCurrentId();
        };

        // Device ID Management Functions
        function getCurrentId() {
            var xhttp = new XMLHttpRequest();
            xhttp.onreadystatechange = function() {
                if (xhttp.readyState == 4) {
                    if (xhttp.status == 200) {
                        try {
                            var response = JSON.parse(xhttp.responseText);
                            document.getElementById("currentIdDisplay").innerHTML =
                                "Current Device ID: <strong>" + response.device_id + "</strong>";
                        } catch (e) {
                            document.getElementById("currentIdDisplay").innerHTML =
                                "Current Device ID: <strong>" + xhttp.responseText + "</strong>";
                        }
                    } else {
                        document.getElementById("currentIdDisplay").innerHTML =
                            "Current Device ID: <span style='color: red;'>Error loading ID</span>";
                    }
                }
            };
            xhttp.open("GET", "/api/device_id", true);
            xhttp.send();
        }

        function setDeviceId() {
            var deviceId = document.getElementById("deviceId").value.trim();

            if (deviceId === "") {
                alert("Please enter a device ID!");
                return;
            }

            var id = parseInt(deviceId);
            if (isNaN(id) || id < 1 || id > 255) {
                alert("Device ID must be a number between 1 and 255!");
                return;
            }

            if (!confirm("Are you sure you want to change the device ID to " + id + "?")) {
                return;
            }

            var xhttp = new XMLHttpRequest();
            xhttp.onreadystatechange = function() {
                if (xhttp.readyState == 4) {
                    if (xhttp.status == 200) {
                        alert("Device ID updated successfully!");
                        document.getElementById("deviceId").value = "";
                        getCurrentId(); // Refresh the display
                    } else {
                        alert("Failed to update device ID: " + xhttp.responseText);
                    }
                }
            };
            xhttp.open("POST", "/api/device_id", true);
            xhttp.setRequestHeader("Content-Type", "application/json");
            xhttp.send(JSON.stringify({device_id: id}));
        }

        // File Upload Functions
        function setpath() {
            var default_path = document.getElementById("newfile").files[0].name;
            document.getElementById("filepath").value = default_path;
        }

        function upload() {
            var filePath = document.getElementById("filepath").value;
            var upload_path = "/upload/" + filePath;
            var fileInput = document.getElementById("newfile").files;

            /* Max size of an individual file. Make sure this
             * value is same as that set in file_server.c */
            var MAX_FILE_SIZE = 512*1024;
            var MAX_FILE_SIZE_STR = "512KB";

            if (fileInput.length == 0) {
                alert("No file selected!");
            } else if (filePath.length == 0) {
                alert("File path on server is not set!");
            } else if (filePath.indexOf(' ') >= 0) {
                alert("File path on server cannot have spaces!");
            } else if (filePath[filePath.length-1] == '/') {
                alert("File name not specified after path!");
            } else if (fileInput[0].size > MAX_FILE_SIZE) {
                alert("File size must be less than " + MAX_FILE_SIZE_STR + "!");
            } else {
                document.getElementById("newfile").disabled = true;
                document.getElementById("filepath").disabled = true;
                document.getElementById("upload").disabled = true;
                document.getElementById("upload").innerHTML = "Uploading...";

                var file = fileInput[0];
                var xhttp = new XMLHttpRequest();
                xhttp.onreadystatechange = function() {
                    if (xhttp.readyState == 4) {
                        if (xhttp.status == 200) {
                            alert("File uploaded successfully!");
                            document.getElementById("newfile").disabled = false;
                            document.getElementById("filepath").disabled = false;
                            document.getElementById("upload").disabled = false;
                            document.getElementById("upload").innerHTML = "Upload";
                            document.getElementById("newfile").value = "";
                            document.getElementById("filepath").value = "";
                        } else if (xhttp.status == 0) {
                            alert("Server closed the connection abruptly!");
                            location.reload()
                        } else {
                            alert(xhttp.status + " Error!\n" + xhttp.responseText);
                            document.getElementById("newfile").disabled = false;
                            document.getElementById("filepath").disabled = false;
                            document.getElementById("upload").disabled = false;
                            document.getElementById("upload").innerHTML = "Upload";
                        }
                    }
                };
                xhttp.open("POST", upload_path, true);
                xhttp.send(file);
            }
        }

        // Firmware Update Functions
        function uploadFirmware() {
            var fileInput = document.getElementById("firmwarefile").files;

            if (fileInput.length == 0) {
                alert("No firmware file selected!");
                return;
            }

            var file = fileInput[0];

            // Check if file has .bin extension
            if (!file.name.toLowerCase().endsWith('.bin')) {
                alert("Please select a valid firmware file with .bin extension!");
                return;
            }

            // Check firmware file size (2MB limit)
            if (file.size > 2*1024*1024) {
                alert("Firmware file size must be less than 2MB!");
                return;
            }

            // Confirm firmware update
            if (!confirm("⚠️ Are you sure you want to update the firmware?\n\nThis will:\n- Restart the device\n- Temporarily disconnect all connections\n- Take about 30-60 seconds to complete\n\nProceed?")) {
                return;
            }

            document.getElementById("firmwarefile").disabled = true;
            document.getElementById("otaupload").disabled = true;
            document.getElementById("otaupload").innerHTML = "🔄 Updating...";

            var xhttp = new XMLHttpRequest();

            // Add progress tracking
            xhttp.upload.addEventListener("progress", function(e) {
                if (e.lengthComputable) {
                    var percentComplete = (e.loaded / e.total) * 100;
                    document.getElementById("otaupload").innerHTML = "🔄 Uploading... " + Math.round(percentComplete) + "%";
                }
            });

            xhttp.onreadystatechange = function() {
                if (xhttp.readyState == 4) {
                    if (xhttp.status == 200) {
                        document.getElementById("otaupload").innerHTML = "✅ Update Successful!";
                        alert("✅ Firmware update successful!\n\nThe device will restart automatically.\nPlease wait about 30 seconds before refreshing the page.");
                        setTimeout(function() {
                            location.reload();
                        }, 15000);
                    } else if (xhttp.status == 0) {
                        document.getElementById("otaupload").innerHTML = "🔄 Device Restarting...";
                        alert("Device is restarting after firmware update...\nPage will reload automatically.");
                        setTimeout(function() {
                            location.reload();
                        }, 10000);
                    } else {
                        alert("❌ Firmware update failed!\n\nError: " + xhttp.status + " - " + xhttp.responseText);
                        document.getElementById("firmwarefile").disabled = false;
                        document.getElementById("otaupload").disabled = false;
                        document.getElementById("otaupload").innerHTML = "Update Firmware";
                    }
                }
            };

            xhttp.open("POST", "/ota", true);
            xhttp.send(file);
        }

        // Utility function to validate input
        function validateInput(input, min, max, fieldName) {
            var value = parseInt(input);
            if (isNaN(value) || value < min || value > max) {
                alert(fieldName + " must be a number between " + min + " and " + max + "!");
                return false;
            }
            return true;
        }

        // Add keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl+R or F5 to refresh device ID
            if ((e.ctrlKey && e.key === 'r') || e.key === 'F5') {
                e.preventDefault();
                getCurrentId();
            }
        });
    </script>
</body>
</html>
