-- ccache will be used for faster recompilation
-- Building ESP-IDF components for target esp32c3
-- Project sdkconfig file E:/esp32_space/ble_mill_monitor_slave/sdkconfig
Loading defaults file E:/esp32_space/ble_mill_monitor_slave/sdkconfig.defaults...
warning: unknown kconfig symbol 'BT_LE_50_FEATURE_SUPPORT' assigned to 'n' in E:/esp32_space/ble_mill_monitor_slave/sdkconfig.defaults
Loading defaults file E:/esp32_space/ble_mill_monitor_slave/sdkconfig.defaults.esp32c3...
warning: unknown kconfig symbol 'BT_LE_50_FEATURE_SUPPORT' assigned to 'n' in E:/esp32_space/ble_mill_monitor_slave/sdkconfig.defaults.esp32c3
-- Adding linker script E:/esp/v5.3.3/esp-idf/components/riscv/ld/rom.api.ld
-- App "ble_slave" version: f8c71a4
-- Adding linker script E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/esp_system/ld/memory.ld
-- Adding linker script E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/esp_system/ld/sections.ld.in
-- Adding linker script E:/esp/v5.3.3/esp-idf/components/esp_rom/esp32c3/ld/esp32c3.rom.ld
-- Adding linker script E:/esp/v5.3.3/esp-idf/components/esp_rom/esp32c3/ld/esp32c3.rom.api.ld
-- Adding linker script E:/esp/v5.3.3/esp-idf/components/esp_rom/esp32c3/ld/esp32c3.rom.bt_funcs.ld
-- Adding linker script E:/esp/v5.3.3/esp-idf/components/esp_rom/esp32c3/ld/esp32c3.rom.libgcc.ld
-- Adding linker script E:/esp/v5.3.3/esp-idf/components/esp_rom/esp32c3/ld/esp32c3.rom.version.ld
-- Adding linker script E:/esp/v5.3.3/esp-idf/components/esp_rom/esp32c3/ld/esp32c3.rom.eco3.ld
-- Adding linker script E:/esp/v5.3.3/esp-idf/components/esp_rom/esp32c3/ld/esp32c3.rom.eco3_bt_funcs.ld
-- Adding linker script E:/esp/v5.3.3/esp-idf/components/esp_rom/esp32c3/ld/esp32c3.rom.newlib.ld
-- Adding linker script E:/esp/v5.3.3/esp-idf/components/soc/esp32c3/ld/esp32c3.peripherals.ld
-- Components: app_trace app_update bootloader bootloader_support bt cmock console cxx driver efuse esp-tls esp_adc esp_app_format esp_bootloader_format esp_coex esp_common esp_driver_ana_cmpr esp_driver_cam esp_driver_dac esp_driver_gpio esp_driver_gptimer esp_driver_i2c esp_driver_i2s esp_driver_isp esp_driver_jpeg esp_driver_ledc esp_driver_mcpwm esp_driver_parlio esp_driver_pcnt esp_driver_ppa esp_driver_rmt esp_driver_sdio esp_driver_sdm esp_driver_sdmmc esp_driver_sdspi esp_driver_spi esp_driver_touch_sens esp_driver_tsens esp_driver_uart esp_driver_usb_serial_jtag esp_eth esp_event esp_gdbstub esp_hid esp_http_client esp_http_server esp_https_ota esp_https_server esp_hw_support esp_lcd esp_local_ctrl esp_mm esp_netif esp_netif_stack esp_partition esp_phy esp_pm esp_psram esp_ringbuf esp_rom esp_system esp_timer esp_vfs_console esp_wifi espcoredump esptool_py fatfs freertos hal heap http_parser idf_test ieee802154 json log lwip main mbedtls mqtt newlib nvs_flash nvs_sec_provider openthread partition_table protobuf-c protocomm pthread riscv sdmmc soc spi_flash spiffs tcp_transport ulp unity usb vfs wear_levelling wifi_provisioning wpa_supplicant
-- Component paths: E:/esp/v5.3.3/esp-idf/components/app_trace E:/esp/v5.3.3/esp-idf/components/app_update E:/esp/v5.3.3/esp-idf/components/bootloader E:/esp/v5.3.3/esp-idf/components/bootloader_support E:/esp/v5.3.3/esp-idf/components/bt E:/esp/v5.3.3/esp-idf/components/cmock E:/esp/v5.3.3/esp-idf/components/console E:/esp/v5.3.3/esp-idf/components/cxx E:/esp/v5.3.3/esp-idf/components/driver E:/esp/v5.3.3/esp-idf/components/efuse E:/esp/v5.3.3/esp-idf/components/esp-tls E:/esp/v5.3.3/esp-idf/components/esp_adc E:/esp/v5.3.3/esp-idf/components/esp_app_format E:/esp/v5.3.3/esp-idf/components/esp_bootloader_format E:/esp/v5.3.3/esp-idf/components/esp_coex E:/esp/v5.3.3/esp-idf/components/esp_common E:/esp/v5.3.3/esp-idf/components/esp_driver_ana_cmpr E:/esp/v5.3.3/esp-idf/components/esp_driver_cam E:/esp/v5.3.3/esp-idf/components/esp_driver_dac E:/esp/v5.3.3/esp-idf/components/esp_driver_gpio E:/esp/v5.3.3/esp-idf/components/esp_driver_gptimer E:/esp/v5.3.3/esp-idf/components/esp_driver_i2c E:/esp/v5.3.3/esp-idf/components/esp_driver_i2s E:/esp/v5.3.3/esp-idf/components/esp_driver_isp E:/esp/v5.3.3/esp-idf/components/esp_driver_jpeg E:/esp/v5.3.3/esp-idf/components/esp_driver_ledc E:/esp/v5.3.3/esp-idf/components/esp_driver_mcpwm E:/esp/v5.3.3/esp-idf/components/esp_driver_parlio E:/esp/v5.3.3/esp-idf/components/esp_driver_pcnt E:/esp/v5.3.3/esp-idf/components/esp_driver_ppa E:/esp/v5.3.3/esp-idf/components/esp_driver_rmt E:/esp/v5.3.3/esp-idf/components/esp_driver_sdio E:/esp/v5.3.3/esp-idf/components/esp_driver_sdm E:/esp/v5.3.3/esp-idf/components/esp_driver_sdmmc E:/esp/v5.3.3/esp-idf/components/esp_driver_sdspi E:/esp/v5.3.3/esp-idf/components/esp_driver_spi E:/esp/v5.3.3/esp-idf/components/esp_driver_touch_sens E:/esp/v5.3.3/esp-idf/components/esp_driver_tsens E:/esp/v5.3.3/esp-idf/components/esp_driver_uart E:/esp/v5.3.3/esp-idf/components/esp_driver_usb_serial_jtag E:/esp/v5.3.3/esp-idf/components/esp_eth E:/esp/v5.3.3/esp-idf/components/esp_event E:/esp/v5.3.3/esp-idf/components/esp_gdbstub E:/esp/v5.3.3/esp-idf/components/esp_hid E:/esp/v5.3.3/esp-idf/components/esp_http_client E:/esp/v5.3.3/esp-idf/components/esp_http_server E:/esp/v5.3.3/esp-idf/components/esp_https_ota E:/esp/v5.3.3/esp-idf/components/esp_https_server E:/esp/v5.3.3/esp-idf/components/esp_hw_support E:/esp/v5.3.3/esp-idf/components/esp_lcd E:/esp/v5.3.3/esp-idf/components/esp_local_ctrl E:/esp/v5.3.3/esp-idf/components/esp_mm E:/esp/v5.3.3/esp-idf/components/esp_netif E:/esp/v5.3.3/esp-idf/components/esp_netif_stack E:/esp/v5.3.3/esp-idf/components/esp_partition E:/esp/v5.3.3/esp-idf/components/esp_phy E:/esp/v5.3.3/esp-idf/components/esp_pm E:/esp/v5.3.3/esp-idf/components/esp_psram E:/esp/v5.3.3/esp-idf/components/esp_ringbuf E:/esp/v5.3.3/esp-idf/components/esp_rom E:/esp/v5.3.3/esp-idf/components/esp_system E:/esp/v5.3.3/esp-idf/components/esp_timer E:/esp/v5.3.3/esp-idf/components/esp_vfs_console E:/esp/v5.3.3/esp-idf/components/esp_wifi E:/esp/v5.3.3/esp-idf/components/espcoredump E:/esp/v5.3.3/esp-idf/components/esptool_py E:/esp/v5.3.3/esp-idf/components/fatfs E:/esp/v5.3.3/esp-idf/components/freertos E:/esp/v5.3.3/esp-idf/components/hal E:/esp/v5.3.3/esp-idf/components/heap E:/esp/v5.3.3/esp-idf/components/http_parser E:/esp/v5.3.3/esp-idf/components/idf_test E:/esp/v5.3.3/esp-idf/components/ieee802154 E:/esp/v5.3.3/esp-idf/components/json E:/esp/v5.3.3/esp-idf/components/log E:/esp/v5.3.3/esp-idf/components/lwip E:/esp32_space/ble_mill_monitor_slave/main E:/esp/v5.3.3/esp-idf/components/mbedtls E:/esp/v5.3.3/esp-idf/components/mqtt E:/esp/v5.3.3/esp-idf/components/newlib E:/esp/v5.3.3/esp-idf/components/nvs_flash E:/esp/v5.3.3/esp-idf/components/nvs_sec_provider E:/esp/v5.3.3/esp-idf/components/openthread E:/esp/v5.3.3/esp-idf/components/partition_table E:/esp/v5.3.3/esp-idf/components/protobuf-c E:/esp/v5.3.3/esp-idf/components/protocomm E:/esp/v5.3.3/esp-idf/components/pthread E:/esp/v5.3.3/esp-idf/components/riscv E:/esp/v5.3.3/esp-idf/components/sdmmc E:/esp/v5.3.3/esp-idf/components/soc E:/esp/v5.3.3/esp-idf/components/spi_flash E:/esp/v5.3.3/esp-idf/components/spiffs E:/esp/v5.3.3/esp-idf/components/tcp_transport E:/esp/v5.3.3/esp-idf/components/ulp E:/esp/v5.3.3/esp-idf/components/unity E:/esp/v5.3.3/esp-idf/components/usb E:/esp/v5.3.3/esp-idf/components/vfs E:/esp/v5.3.3/esp-idf/components/wear_levelling E:/esp/v5.3.3/esp-idf/components/wifi_provisioning E:/esp/v5.3.3/esp-idf/components/wpa_supplicant
-- Configuring done (8.5s)
-- Generating done (1.0s)
-- Build files have been written to: E:/esp32_space/ble_mill_monitor_slave/build
